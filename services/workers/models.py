"""
Minimal models for Celery workers.

This module provides simplified versions of the API models
that Celery workers need, without requiring the full API codebase.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional


class VMStatus(Enum):
    """VM Status enumeration - simplified version for workers."""

    CREATING = "creating"
    RUNNING = "running"
    MONITORING = "monitoring"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    FAILED = "failed"
    EXPIRED = "expired"
    INJECTING = "injecting"
    FILE_INJECTING = "file_injecting"


class FileStatus(Enum):
    """File Status enumeration - simplified version for workers."""

    UPLOADED = "uploaded"
    PROCESSING = "processing"
    INJECTED = "injected"
    EXECUTED = "executed"
    ERROR = "error"


class WorkflowStatus(Enum):
    """Workflow Status enumeration - simplified version for workers."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    MONITORING = "monitoring"
    FILE_INJECTING = "file_injecting"


# Simple data classes for worker tasks
class VMInstance:
    """Simplified VM instance for workers."""

    def __init__(self, vm_id: str, vm_name: str, status: VMStatus = VMStatus.CREATING):
        self.vm_id = vm_id
        self.vm_name = vm_name
        self.status = status
        self.ip_address: str | None = None
        self.injection_path: str | None = None
        self.created_at = datetime.now()

    def to_dict(self) -> dict[str, Any]:
        return {
            "vm_id": self.vm_id,
            "vm_name": self.vm_name,
            "status": self.status.value
            if isinstance(self.status, VMStatus)
            else self.status,
            "ip_address": self.ip_address,
            "injection_path": self.injection_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class FileInstance:
    """Simplified file instance for workers."""

    def __init__(
        self, file_id: str, filename: str, status: FileStatus = FileStatus.UPLOADED
    ):
        self.file_id = file_id
        self.filename = filename
        self.status = status
        self.file_size: int | None = None
        self.file_path: str | None = None
        self.created_at = datetime.now()

    def to_dict(self) -> dict[str, Any]:
        return {
            "file_id": self.file_id,
            "filename": self.filename,
            "status": self.status.value
            if isinstance(self.status, FileStatus)
            else self.status,
            "file_size": self.file_size,
            "file_path": self.file_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class WorkflowJob:
    """Simplified workflow job for workers."""

    def __init__(
        self,
        job_id: str,
        job_type: str,
        status: WorkflowStatus = WorkflowStatus.PENDING,
    ):
        self.job_id = job_id
        self.job_type = job_type
        self.status = status
        self.vm_id: str | None = None
        self.file_id: str | None = None
        self.created_at = datetime.now()
        self.started_at: datetime | None = None
        self.completed_at: datetime | None = None
        self.error_message: str | None = None

    def to_dict(self) -> dict[str, Any]:
        return {
            "job_id": self.job_id,
            "job_type": self.job_type,
            "status": self.status.value
            if isinstance(self.status, WorkflowStatus)
            else self.status,
            "vm_id": self.vm_id,
            "file_id": self.file_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat()
            if self.completed_at
            else None,
            "error_message": self.error_message,
        }
