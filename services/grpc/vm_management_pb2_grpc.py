# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import warnings

import grpc
import vm_management_pb2 as vm__management__pb2

GRPC_GENERATED_VERSION = "1.67.0"
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower

    _version_not_supported = first_version_is_lower(
        GRPC_VERSION, GRPC_GENERATED_VERSION
    )
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f"The grpc package installed is at version {GRPC_VERSION},"
        + " but the generated code in vm_management_pb2_grpc.py depends on"
        + f" grpcio>={GRPC_GENERATED_VERSION}."
        + f" Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}"
        + f" or downgrade your generated code using grpcio-tools<={GRPC_VERSION}."
    )


class VMManagementStub:
    """VM Management Service for TurdParty"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateVM = channel.unary_unary(
            "/turdparty.vm.VMManagement/CreateVM",
            request_serializer=vm__management__pb2.CreateVMRequest.SerializeToString,
            response_deserializer=vm__management__pb2.CreateVMResponse.FromString,
            _registered_method=True,
        )
        self.GetVMStatus = channel.unary_unary(
            "/turdparty.vm.VMManagement/GetVMStatus",
            request_serializer=vm__management__pb2.GetVMStatusRequest.SerializeToString,
            response_deserializer=vm__management__pb2.GetVMStatusResponse.FromString,
            _registered_method=True,
        )
        self.StartVM = channel.unary_unary(
            "/turdparty.vm.VMManagement/StartVM",
            request_serializer=vm__management__pb2.StartVMRequest.SerializeToString,
            response_deserializer=vm__management__pb2.StartVMResponse.FromString,
            _registered_method=True,
        )
        self.StopVM = channel.unary_unary(
            "/turdparty.vm.VMManagement/StopVM",
            request_serializer=vm__management__pb2.StopVMRequest.SerializeToString,
            response_deserializer=vm__management__pb2.StopVMResponse.FromString,
            _registered_method=True,
        )
        self.DestroyVM = channel.unary_unary(
            "/turdparty.vm.VMManagement/DestroyVM",
            request_serializer=vm__management__pb2.DestroyVMRequest.SerializeToString,
            response_deserializer=vm__management__pb2.DestroyVMResponse.FromString,
            _registered_method=True,
        )
        self.InjectFile = channel.unary_unary(
            "/turdparty.vm.VMManagement/InjectFile",
            request_serializer=vm__management__pb2.InjectFileRequest.SerializeToString,
            response_deserializer=vm__management__pb2.InjectFileResponse.FromString,
            _registered_method=True,
        )
        self.ExecuteCommand = channel.unary_stream(
            "/turdparty.vm.VMManagement/ExecuteCommand",
            request_serializer=vm__management__pb2.ExecuteCommandRequest.SerializeToString,
            response_deserializer=vm__management__pb2.ExecuteCommandResponse.FromString,
            _registered_method=True,
        )
        self.GetVMMetrics = channel.unary_unary(
            "/turdparty.vm.VMManagement/GetVMMetrics",
            request_serializer=vm__management__pb2.GetVMMetricsRequest.SerializeToString,
            response_deserializer=vm__management__pb2.GetVMMetricsResponse.FromString,
            _registered_method=True,
        )
        self.StreamVMMetrics = channel.unary_stream(
            "/turdparty.vm.VMManagement/StreamVMMetrics",
            request_serializer=vm__management__pb2.StreamVMMetricsRequest.SerializeToString,
            response_deserializer=vm__management__pb2.VMMetricsUpdate.FromString,
            _registered_method=True,
        )


class VMManagementServicer:
    """VM Management Service for TurdParty"""

    def CreateVM(self, request, context):
        """VM Lifecycle Operations"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetVMStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def StartVM(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def StopVM(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DestroyVM(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def InjectFile(self, request, context):
        """File Operations"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def ExecuteCommand(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetVMMetrics(self, request, context):
        """Monitoring"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def StreamVMMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_VMManagementServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "CreateVM": grpc.unary_unary_rpc_method_handler(
            servicer.CreateVM,
            request_deserializer=vm__management__pb2.CreateVMRequest.FromString,
            response_serializer=vm__management__pb2.CreateVMResponse.SerializeToString,
        ),
        "GetVMStatus": grpc.unary_unary_rpc_method_handler(
            servicer.GetVMStatus,
            request_deserializer=vm__management__pb2.GetVMStatusRequest.FromString,
            response_serializer=vm__management__pb2.GetVMStatusResponse.SerializeToString,
        ),
        "StartVM": grpc.unary_unary_rpc_method_handler(
            servicer.StartVM,
            request_deserializer=vm__management__pb2.StartVMRequest.FromString,
            response_serializer=vm__management__pb2.StartVMResponse.SerializeToString,
        ),
        "StopVM": grpc.unary_unary_rpc_method_handler(
            servicer.StopVM,
            request_deserializer=vm__management__pb2.StopVMRequest.FromString,
            response_serializer=vm__management__pb2.StopVMResponse.SerializeToString,
        ),
        "DestroyVM": grpc.unary_unary_rpc_method_handler(
            servicer.DestroyVM,
            request_deserializer=vm__management__pb2.DestroyVMRequest.FromString,
            response_serializer=vm__management__pb2.DestroyVMResponse.SerializeToString,
        ),
        "InjectFile": grpc.unary_unary_rpc_method_handler(
            servicer.InjectFile,
            request_deserializer=vm__management__pb2.InjectFileRequest.FromString,
            response_serializer=vm__management__pb2.InjectFileResponse.SerializeToString,
        ),
        "ExecuteCommand": grpc.unary_stream_rpc_method_handler(
            servicer.ExecuteCommand,
            request_deserializer=vm__management__pb2.ExecuteCommandRequest.FromString,
            response_serializer=vm__management__pb2.ExecuteCommandResponse.SerializeToString,
        ),
        "GetVMMetrics": grpc.unary_unary_rpc_method_handler(
            servicer.GetVMMetrics,
            request_deserializer=vm__management__pb2.GetVMMetricsRequest.FromString,
            response_serializer=vm__management__pb2.GetVMMetricsResponse.SerializeToString,
        ),
        "StreamVMMetrics": grpc.unary_stream_rpc_method_handler(
            servicer.StreamVMMetrics,
            request_deserializer=vm__management__pb2.StreamVMMetricsRequest.FromString,
            response_serializer=vm__management__pb2.VMMetricsUpdate.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "turdparty.vm.VMManagement", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers(
        "turdparty.vm.VMManagement", rpc_method_handlers
    )


# This class is part of an EXPERIMENTAL API.
class VMManagement:
    """VM Management Service for TurdParty"""

    @staticmethod
    def CreateVM(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/CreateVM",
            vm__management__pb2.CreateVMRequest.SerializeToString,
            vm__management__pb2.CreateVMResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def GetVMStatus(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/GetVMStatus",
            vm__management__pb2.GetVMStatusRequest.SerializeToString,
            vm__management__pb2.GetVMStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def StartVM(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/StartVM",
            vm__management__pb2.StartVMRequest.SerializeToString,
            vm__management__pb2.StartVMResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def StopVM(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/StopVM",
            vm__management__pb2.StopVMRequest.SerializeToString,
            vm__management__pb2.StopVMResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def DestroyVM(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/DestroyVM",
            vm__management__pb2.DestroyVMRequest.SerializeToString,
            vm__management__pb2.DestroyVMResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def InjectFile(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/InjectFile",
            vm__management__pb2.InjectFileRequest.SerializeToString,
            vm__management__pb2.InjectFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def ExecuteCommand(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/turdparty.vm.VMManagement/ExecuteCommand",
            vm__management__pb2.ExecuteCommandRequest.SerializeToString,
            vm__management__pb2.ExecuteCommandResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def GetVMMetrics(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/turdparty.vm.VMManagement/GetVMMetrics",
            vm__management__pb2.GetVMMetricsRequest.SerializeToString,
            vm__management__pb2.GetVMMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )

    @staticmethod
    def StreamVMMetrics(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/turdparty.vm.VMManagement/StreamVMMetrics",
            vm__management__pb2.StreamVMMetricsRequest.SerializeToString,
            vm__management__pb2.VMMetricsUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )
