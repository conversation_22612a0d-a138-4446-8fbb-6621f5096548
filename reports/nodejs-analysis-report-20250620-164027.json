{"metadata": {"report_id": "nodejs-55505f01", "generated_at": "2025-06-20T14:40:27.923100+00:00", "report_type": "Node.js Analysis", "data_sources": ["TurdParty API", "Elasticsearch"], "service_urls": {"api": "http://api.turdparty.localhost", "elasticsearch": "http://elasticsearch.turdparty.localhost"}}, "file_analysis": {"file_id": "55505f01-539a-4d96-98bd-8b7ed8bac0a4", "filename": "node-v20.10.0-x64.msi", "status": "injected", "created_at": "2025-06-20T05:13:37.180018", "analysis_type": "Windows Installer Analysis"}, "vm_environment": {"vm_id": "a08f927f-3a1e-4c9c-8192-6817d9c3cea3", "vm_name": "win10-nodejs-ccee4a7a", "vm_status": "creating", "platform": "Windows 10", "analysis_method": "Real VM Injection"}, "execution_analysis": {"total_events": 12, "event_summary": {"event_types": {"installation": 4, "info": 8}, "actions": {"file_injection": 4, "log_message": 8}, "timeline": {"first_event": "2025-06-19T20:32:29.490824Z", "last_event": "2025-06-20T05:14:03.283778Z", "total_events": 12}}, "monitoring_status": "Active"}, "security_assessment": {"risk_level": "Low", "threat_indicators": [], "behavioral_analysis": "Standard software installation pattern"}, "recommendations": ["Node.js installation appears to be legitimate software", "Monitor for any unexpected network connections", "Verify installation integrity through official channels", "Review installed packages and dependencies"]}