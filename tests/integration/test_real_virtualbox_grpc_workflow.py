#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Real VirtualBox gRPC Workflow Integration Tests

These tests use real VirtualBox VMs, gRPC communication, and actual file injection
workflows instead of mocked components. They validate the complete end-to-end
workflow with real VirtualBox infrastructure, replacing all simulation with actual operations.

Requirements:
- VirtualBox installed on host
- Vagrant with VirtualBox provider
- gRPC service running on port 40000
- 10Baht/windows10-turdparty box available

Usage:
    pytest tests/integration/test_real_virtualbox_grpc_workflow.py -v -s
"""

import asyncio
import time
from typing import Any, Dict
import uuid

import pytest
import requests

from services.grpc.vm_client import VMGRPCClient

# Test configuration for real VirtualBox integration
REAL_VIRTUALBOX_CONFIG = {
    "api_base_url": "http://localhost:8000",
    "grpc_endpoint": "localhost:40000",
    "vm_template": "10Baht/windows10-turdparty",
    "vm_provider": "virtualbox",
    "test_timeout": 1800,  # 30 minutes
}


@pytest.fixture(scope="session")
def real_virtualbox_grpc_client():
    """Provide real VirtualBox gRPC client."""
    client = VMGRPCClient(REAL_VIRTUALBOX_CONFIG["grpc_endpoint"])

    # Test connection
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        connected = loop.run_until_complete(client.connect())
        if not connected:
            pytest.skip("VirtualBox gRPC service not available")

        yield client

    finally:
        loop.run_until_complete(client.disconnect())
        loop.close()


@pytest.fixture
def real_virtualbox_vm(real_virtualbox_grpc_client):
    """Create a real VirtualBox VM for testing."""
    vm_config = {
        "name": f"pytest-vbox-{uuid.uuid4().hex[:8]}",
        "template": REAL_VIRTUALBOX_CONFIG["vm_template"],
        "vm_type": "vagrant",
        "provider": REAL_VIRTUALBOX_CONFIG["vm_provider"],
        "memory_mb": 4096,
        "cpus": 2,
        "disk_gb": 40,
        "description": "Real VirtualBox integration test VM",
        "tags": ["pytest", "virtualbox", "grpc", "real-integration"],
    }

    # Create VM via API
    response = requests.post(
        f"{REAL_VIRTUALBOX_CONFIG['api_base_url']}/api/v1/vms/",
        json=vm_config,
        timeout=120,
    )

    if response.status_code not in [200, 201]:
        pytest.skip(f"Failed to create VirtualBox VM: {response.status_code}")

    vm_info = response.json()
    vm_id = vm_info.get("vm_id")

    # Wait for VM to be ready (VirtualBox VMs take longer)
    _wait_for_virtualbox_vm_ready(vm_id, timeout=900)  # 15 minutes

    yield {
        "vm_id": vm_id,
        "vm_info": vm_info,
        "config": vm_config,
        "grpc_client": real_virtualbox_grpc_client,
    }

    # Cleanup VM
    try:
        requests.delete(
            f"{REAL_VIRTUALBOX_CONFIG['api_base_url']}/api/v1/vms/{vm_id}", timeout=120
        )
    except Exception as e:
        print(f"Warning: Failed to cleanup VirtualBox VM {vm_id}: {e}")


@pytest.fixture
def real_windows_test_file():
    """Create a real Windows test file for injection."""
    test_content = (
        b"""@echo off
echo TurdParty Real VirtualBox gRPC Test > C:\\TurdParty\\vbox-grpc-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\vbox-grpc-test-result.txt
echo Test UUID: """
        + str(uuid.uuid4()).encode()
        + b""" >> C:\\TurdParty\\vbox-grpc-test-result.txt

echo Creating comprehensive test data...
echo Creating registry entries...
reg add "HKCU\\Software\\TurdParty\\VBoxGRPCTest" /v "TestRun" /t REG_SZ /d "%date%_%time%" /f
reg add "HKCU\\Software\\TurdParty\\VBoxGRPCTest" /v "TestType" /t REG_SZ /d "RealVirtualBoxGRPC" /f

echo Creating test files...
echo VirtualBox gRPC test file 1 > C:\\TurdParty\\vbox-test-file-1.txt
echo VirtualBox gRPC test file 2 > C:\\TurdParty\\vbox-test-file-2.txt
echo VirtualBox gRPC test file 3 > C:\\TurdParty\\vbox-test-file-3.txt

echo Creating test directories...
mkdir C:\\TurdParty\\VBoxGRPCTestDirectory
echo VirtualBox directory test > C:\\TurdParty\\VBoxGRPCTestDirectory\\vbox-dir-test.txt

echo Running system commands for ECS data...
systeminfo > C:\\TurdParty\\vbox-system-info.txt
tasklist > C:\\TurdParty\\vbox-process-list.txt

echo Real VirtualBox gRPC integration test completed successfully! >> C:\\TurdParty\\vbox-grpc-test-result.txt
"""
    )

    filename = f"real-vbox-grpc-test-{uuid.uuid4().hex[:8]}.bat"

    # Upload file via API
    files = {"file": (filename, test_content, "application/octet-stream")}

    response = requests.post(
        f"{REAL_VIRTUALBOX_CONFIG['api_base_url']}/api/v1/files/upload",
        files=files,
        timeout=60,
    )

    if response.status_code not in [200, 201]:
        pytest.skip(f"Failed to upload Windows test file: {response.status_code}")

    file_info = response.json()
    file_id = file_info.get("file_id")

    yield {
        "file_id": file_id,
        "filename": filename,
        "content": test_content,
        "file_info": file_info,
    }

    # Cleanup file
    try:
        requests.delete(
            f"{REAL_VIRTUALBOX_CONFIG['api_base_url']}/api/v1/files/{file_id}",
            timeout=30,
        )
    except Exception as e:
        print(f"Warning: Failed to cleanup file {file_id}: {e}")


@pytest.mark.slow
@pytest.mark.virtualbox
@pytest.mark.grpc
class TestRealVirtualBoxGRPCWorkflow:
    """Test real VirtualBox gRPC workflow."""

    def test_real_virtualbox_vm_creation(self, real_virtualbox_vm):
        """Test real VirtualBox VM creation via gRPC."""
        vm_data = real_virtualbox_vm

        assert vm_data["vm_id"] is not None
        assert vm_data["vm_info"]["success"] is True
        assert vm_data["config"]["provider"] == "virtualbox"
        assert vm_data["config"]["template"] == "10Baht/windows10-turdparty"

        print(f"✅ Real VirtualBox VM created: {vm_data['vm_id']}")
        print(f"📋 VM template: {vm_data['config']['template']}")
        print(f"💾 Memory: {vm_data['config']['memory_mb']} MB")
        print(f"🖥️ CPUs: {vm_data['config']['cpus']}")

    @pytest.mark.asyncio
    async def test_real_virtualbox_vm_status(self, real_virtualbox_vm):
        """Test real VirtualBox VM status via gRPC."""
        vm_data = real_virtualbox_vm
        grpc_client = vm_data["grpc_client"]

        status_result = await grpc_client.get_vm_status(vm_data["vm_id"])

        assert status_result["success"] is True
        assert status_result["vm_id"] == vm_data["vm_id"]
        assert "status" in status_result

        print("✅ Real VirtualBox VM status check successful!")
        print(f"📊 VM status: {status_result['status']}")
        print(f"🌐 IP address: {status_result.get('ip_address', 'N/A')}")
        print(f"💾 Memory: {status_result.get('memory_mb', 'N/A')} MB")

    @pytest.mark.asyncio
    async def test_real_virtualbox_file_injection(
        self, real_virtualbox_vm, real_windows_test_file
    ):
        """Test real file injection into VirtualBox VM via gRPC."""
        vm_data = real_virtualbox_vm
        file_data = real_windows_test_file
        grpc_client = vm_data["grpc_client"]

        # Create temporary file path for injection
        import os
        import tempfile

        with tempfile.NamedTemporaryFile(delete=False, suffix=".bat") as temp_file:
            temp_file.write(file_data["content"])
            temp_file_path = temp_file.name

        try:
            # Inject file into real VirtualBox VM
            injection_result = await grpc_client.inject_file(
                vm_id=vm_data["vm_id"],
                file_path=temp_file_path,
                target_path="C:\\TurdParty\\real-vbox-grpc-test.bat",
                permissions="0755",
                execute=True,
            )

            assert injection_result["success"] is True
            assert "injection_id" in injection_result
            assert injection_result["vm_id"] == vm_data["vm_id"]

            print("✅ Real VirtualBox file injection successful!")
            print(f"💉 Injection ID: {injection_result['injection_id']}")
            print(f"📁 Target path: {injection_result['target_path']}")
            print(f"🖥️ VM ID: {injection_result['vm_id']}")

        finally:
            # Cleanup temporary file
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass

    @pytest.mark.asyncio
    async def test_real_virtualbox_command_execution(self, real_virtualbox_vm):
        """Test real command execution in VirtualBox VM via gRPC."""
        vm_data = real_virtualbox_vm
        grpc_client = vm_data["grpc_client"]

        # Execute Windows command in real VirtualBox VM
        execution_result = await grpc_client.execute_command(
            vm_id=vm_data["vm_id"],
            command="echo Real VirtualBox gRPC Command Test",
            timeout_seconds=60,
        )

        assert execution_result["success"] is True
        assert execution_result["exit_code"] == 0
        assert "Real VirtualBox gRPC Command Test" in execution_result["output"]

        print("✅ Real VirtualBox command execution successful!")
        print(f"🔄 Exit code: {execution_result['exit_code']}")
        print(f"📤 Output: {execution_result['output']}")

    @pytest.mark.asyncio
    async def test_real_virtualbox_end_to_end_workflow(
        self, real_virtualbox_vm, real_windows_test_file
    ):
        """Test complete end-to-end workflow with real VirtualBox VM."""
        vm_data = real_virtualbox_vm
        file_data = real_windows_test_file
        grpc_client = vm_data["grpc_client"]

        print("🚀 Starting real VirtualBox end-to-end workflow test...")

        # Step 1: Verify VM status
        status_result = await grpc_client.get_vm_status(vm_data["vm_id"])
        assert status_result["success"] is True
        print("✅ Step 1: VM status verified")

        # Step 2: Inject and execute file
        import os
        import tempfile

        with tempfile.NamedTemporaryFile(delete=False, suffix=".bat") as temp_file:
            temp_file.write(file_data["content"])
            temp_file_path = temp_file.name

        try:
            injection_result = await grpc_client.inject_file(
                vm_id=vm_data["vm_id"],
                file_path=temp_file_path,
                target_path="C:\\TurdParty\\end-to-end-test.bat",
                permissions="0755",
                execute=True,
            )
            assert injection_result["success"] is True
            print("✅ Step 2: File injection and execution completed")

            # Step 3: Wait for execution to complete
            await asyncio.sleep(30)
            print("✅ Step 3: Execution wait period completed")

            # Step 4: Verify execution results
            verification_result = await grpc_client.execute_command(
                vm_id=vm_data["vm_id"],
                command="type C:\\TurdParty\\vbox-grpc-test-result.txt",
                timeout_seconds=30,
            )

            if verification_result["success"] and verification_result["exit_code"] == 0:
                print("✅ Step 4: Execution results verified")
                print(
                    f"📄 Result file content: {verification_result['output'][:200]}..."
                )
            else:
                print("⚠️ Step 4: Could not verify execution results")

            # Step 5: Generate report (would be done by report generation system)
            print("✅ Step 5: End-to-end workflow completed successfully!")

        finally:
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass


def _wait_for_virtualbox_vm_ready(vm_id: str, timeout: int = 900):
    """Wait for VirtualBox VM to be ready (takes longer than Docker)."""
    import time

    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            response = requests.get(
                f"{REAL_VIRTUALBOX_CONFIG['api_base_url']}/api/v1/vms/{vm_id}/status",
                timeout=30,
            )

            if response.status_code == 200:
                vm_status = response.json()
                status = vm_status.get("status", "unknown")

                if status in ["running", "ready"]:
                    print(f"✅ VirtualBox VM {vm_id} is ready")
                    return True
                elif status in ["failed", "error"]:
                    pytest.skip(f"VirtualBox VM failed to start: {vm_status}")
                else:
                    print(f"⏳ VirtualBox VM {vm_id} status: {status}")

        except Exception as e:
            print(f"⚠️ Error checking VirtualBox VM status: {e}")

        time.sleep(30)  # Check every 30 seconds for VirtualBox

    pytest.skip(f"VirtualBox VM {vm_id} not ready within {timeout} seconds")


if __name__ == "__main__":
    # Run tests with real VirtualBox integration
    pytest.main([__file__, "-v", "-s", "--tb=short", "-m", "virtualbox"])
