#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 VirtualBox to Report Integration Test

End-to-end integration test that validates the complete workflow:
1. VirtualBox VM creation via gRPC
2. File injection and execution
3. ECS data collection
4. Real report generation from actual VM execution data

This test replaces all simulation with real VirtualBox operations and report generation.
"""

import asyncio
import json
import os
from pathlib import Path
import subprocess
import sys
import time
import uuid

import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
API_BASE_URL = "http://localhost:8000"
GRPC_PORT = 40000
TEST_TIMEOUT = 1800  # 30 minutes for complete workflow


class VirtualBoxReportIntegrationTester:
    """End-to-end VirtualBox to report integration tester."""

    def __init__(self):
        self.console = Console()
        self.test_vms = []
        self.test_files = []
        self.generated_reports = []

        # Test file for injection
        self.test_file_content = b"""@echo off
echo TurdParty VirtualBox Report Integration Test > C:\\TurdParty\\integration-test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\integration-test-result.txt
echo Creating test registry entries...
reg add "HKCU\\Software\\TurdParty\\IntegrationTest" /v "TestRun" /t REG_SZ /d "%date%_%time%" /f
echo Creating test files...
echo Test file 1 > C:\\TurdParty\\test-file-1.txt
echo Test file 2 > C:\\TurdParty\\test-file-2.txt
mkdir C:\\TurdParty\\TestDirectory
echo Directory test > C:\\TurdParty\\TestDirectory\\dir-test.txt
echo Integration test completed successfully! >> C:\\TurdParty\\integration-test-result.txt
"""
        self.test_file_name = "virtualbox-report-integration-test.bat"
        self.test_file_id = None

    def print_header(self):
        """Print test header."""
        header = Panel(
            "🖥️ [bold blue]VirtualBox to Report Integration Test[/bold blue] 🖥️\n\n"
            "This test validates the complete end-to-end workflow:\n"
            "• VirtualBox VM creation via gRPC\n"
            "• File injection and execution\n"
            "• ECS data collection from real VM\n"
            "• Report generation from actual execution data\n\n"
            "🔗 gRPC Endpoint: localhost:40000\n"
            "📊 Expected: Real reports with actual VM data",
            title="💩🎉 TurdParty Integration Test 🎉💩",
            border_style="blue",
        )
        self.console.print(header)

    def setup_test_file(self) -> bool:
        """Setup test file for injection."""
        self.console.print("[bold blue]📁 Setting up test file...[/bold blue]")

        try:
            # Upload test file
            files = {
                "file": (
                    self.test_file_name,
                    self.test_file_content,
                    "application/octet-stream",
                )
            }

            response = requests.post(
                f"{API_BASE_URL}/api/v1/files/upload", files=files, timeout=60
            )

            if response.status_code in [200, 201]:
                file_info = response.json()
                self.test_file_id = file_info.get("file_id")
                self.test_files.append(self.test_file_id)

                self.console.print(
                    f"[green]✅ Test file uploaded: {self.test_file_id}[/green]"
                )
                return True
            else:
                self.console.print(
                    f"[red]❌ File upload failed: {response.status_code}[/red]"
                )
                return False

        except Exception as e:
            self.console.print(f"[red]❌ File setup error: {e}[/red]")
            return False

    def create_virtualbox_vm(self) -> str:
        """Create VirtualBox VM via gRPC."""
        self.console.print("[bold blue]🖥️ Creating VirtualBox VM...[/bold blue]")

        try:
            vm_config = {
                "name": f"integration-test-vbox-{uuid.uuid4().hex[:8]}",
                "template": "10Baht/windows10-turdparty",
                "vm_type": "vagrant",
                "provider": "virtualbox",
                "memory_mb": 4096,
                "cpus": 2,
                "disk_gb": 40,
                "description": "VirtualBox to Report Integration Test VM",
                "tags": ["integration-test", "virtualbox", "grpc", "report-generation"],
            }

            self.console.print(f"[cyan]📋 Creating VM: {vm_config['name']}[/cyan]")

            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/", json=vm_config, timeout=60
            )

            if response.status_code in [200, 201]:
                vm_info = response.json()
                vm_id = vm_info.get("vm_id")
                self.test_vms.append(vm_id)

                self.console.print(f"[green]✅ VM created: {vm_id}[/green]")
                return vm_id
            else:
                self.console.print(
                    f"[red]❌ VM creation failed: {response.status_code} - {response.text}[/red]"
                )
                return None

        except Exception as e:
            self.console.print(f"[red]❌ VM creation error: {e}[/red]")
            return None

    def inject_and_execute_file(self, vm_id: str) -> bool:
        """Inject and execute file in VM via gRPC."""
        self.console.print(
            "[bold blue]💉 Injecting and executing file via gRPC...[/bold blue]"
        )

        try:
            injection_config = {
                "file_id": self.test_file_id,
                "injection_path": "C:\\TurdParty\\integration-test.bat",
                "execute_after_injection": True,
                "monitor": True,
                "permissions": "0755",
                "metadata": {
                    "test_type": "integration",
                    "vm_provider": "virtualbox_grpc",
                    "expected_outcome": "report_generation",
                },
            }

            self.console.print(
                f"[cyan]📡 Injecting via gRPC on port {GRPC_PORT}[/cyan]"
            )

            response = requests.post(
                f"{API_BASE_URL}/api/v1/vms/{vm_id}/inject",
                json=injection_config,
                timeout=120,
            )

            if response.status_code in [200, 201]:
                injection_info = response.json()
                injection_id = injection_info.get("injection_id")

                self.console.print(
                    f"[green]✅ File injection initiated: {injection_id}[/green]"
                )

                # Wait for injection and execution to complete
                return self.wait_for_execution_complete(injection_id)
            else:
                self.console.print(
                    f"[red]❌ Injection failed: {response.status_code} - {response.text}[/red]"
                )
                return False

        except Exception as e:
            self.console.print(f"[red]❌ Injection error: {e}[/red]")
            return False

    def wait_for_execution_complete(self, injection_id: str) -> bool:
        """Wait for injection and execution to complete."""
        self.console.print(
            f"[yellow]⏳ Monitoring execution {injection_id}...[/yellow]"
        )

        start_time = time.time()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("Processing execution...", total=None)

            while time.time() - start_time < 600:  # 10 minute timeout
                try:
                    response = requests.get(
                        f"{API_BASE_URL}/api/v1/file_injection/{injection_id}/status",
                        timeout=30,
                    )

                    if response.status_code == 200:
                        injection_status = response.json()
                        status = injection_status.get("status", "unknown")

                        progress.update(task, description=f"Execution status: {status}")

                        if status == "COMPLETED":
                            progress.update(task, description="✅ Execution completed")
                            self.console.print(
                                "[green]✅ gRPC injection and execution completed![/green]"
                            )
                            return True
                        elif status in ["FAILED", "ERROR"]:
                            progress.update(task, description="❌ Execution failed")
                            self.console.print(
                                f"[red]❌ Execution failed: {injection_status}[/red]"
                            )
                            return False

                except Exception as e:
                    progress.update(
                        task, description=f"Status check error: {str(e)[:50]}"
                    )

                time.sleep(15)

            progress.update(task, description="❌ Execution timeout")
            self.console.print("[red]❌ Execution timeout[/red]")
            return False

    def generate_real_report(self) -> bool:
        """Generate real report from VM execution data."""
        self.console.print(
            "[bold blue]📊 Generating Real Report from VM Execution...[/bold blue]"
        )

        try:
            # Wait for ECS events to be indexed
            self.console.print(
                "[cyan]⏳ Waiting for ECS events to be indexed (60 seconds)...[/cyan]"
            )
            time.sleep(60)

            # Use the updated report generator that fetches real data
            report_script = project_root / "scripts" / "generate-generic-report.py"

            self.console.print(
                f"[cyan]📡 Running real report generation for {self.test_file_id}[/cyan]"
            )

            # Run report generation script
            result = subprocess.run(
                [sys.executable, str(report_script), self.test_file_id],
                capture_output=True,
                text=True,
                timeout=300,
                cwd=project_root,
                check=False,
            )

            if result.returncode == 0:
                self.console.print(
                    "[green]✅ Real report generated successfully![/green]"
                )
                self.console.print(
                    f"[cyan]📄 Report output: {result.stdout[-500:]}[/cyan]"
                )

                # Try to parse report info from output
                try:
                    if "HTML URL:" in result.stdout:
                        html_url = (
                            result.stdout.split("HTML URL:")[1].split("\n")[0].strip()
                        )
                        self.console.print(f"[cyan]🌐 Report URL: {html_url}[/cyan]")
                        self.generated_reports.append(html_url)
                except Exception:
                    pass

                return True
            else:
                self.console.print(
                    f"[red]❌ Report generation failed: {result.stderr}[/red]"
                )
                return False

        except Exception as e:
            self.console.print(f"[red]❌ Report generation error: {e}[/red]")
            return False

    def cleanup_resources(self):
        """Clean up test resources."""
        self.console.print("[bold blue]🧹 Cleaning up test resources...[/bold blue]")

        # Clean up VMs
        for vm_id in self.test_vms:
            try:
                response = requests.delete(
                    f"{API_BASE_URL}/api/v1/vms/{vm_id}", timeout=60
                )
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up VM: {vm_id}[/green]")
                else:
                    self.console.print(
                        f"[yellow]⚠️ VM cleanup warning: {vm_id}[/yellow]"
                    )
            except Exception as e:
                self.console.print(f"[yellow]⚠️ VM cleanup error: {e}[/yellow]")

        # Clean up files
        for file_id in self.test_files:
            try:
                response = requests.delete(
                    f"{API_BASE_URL}/api/v1/files/{file_id}", timeout=30
                )
                if response.status_code in [200, 204]:
                    self.console.print(f"[green]✅ Cleaned up file: {file_id}[/green]")
                else:
                    self.console.print(
                        f"[yellow]⚠️ File cleanup warning: {file_id}[/yellow]"
                    )
            except Exception as e:
                self.console.print(f"[yellow]⚠️ File cleanup error: {e}[/yellow]")

    def run_complete_integration_test(self) -> bool:
        """Run the complete VirtualBox to report integration test."""
        start_time = time.time()

        self.print_header()

        try:
            # Step 1: Setup test file
            if not self.setup_test_file():
                return False

            # Step 2: Create VirtualBox VM
            vm_id = self.create_virtualbox_vm()
            if not vm_id:
                return False

            # Step 3: Inject and execute file
            if not self.inject_and_execute_file(vm_id):
                return False

            # Step 4: Generate real report
            if not self.generate_real_report():
                return False

            # Success summary
            total_time = time.time() - start_time

            success_panel = Panel(
                f"🎉 [bold green]VirtualBox to Report Integration Test Completed![/bold green] 🎉\n\n"
                f"⏱️ Total Time: {total_time:.2f} seconds\n"
                f"🖥️ VM ID: {vm_id}\n"
                f"📁 File ID: {self.test_file_id}\n"
                f"📊 Reports Generated: {len(self.generated_reports)}\n"
                f"🔗 gRPC Port: {GRPC_PORT}\n\n"
                f"✅ End-to-end workflow verified with real data!",
                title="🖥️ Integration Test Complete",
                border_style="green",
            )
            self.console.print(success_panel)

            return True

        except Exception as e:
            error_msg = f"Integration test failed: {e}"
            self.console.print(f"[red]❌ {error_msg}[/red]")
            return False
        finally:
            self.cleanup_resources()


def main():
    """Main function to run VirtualBox to report integration test."""
    tester = VirtualBoxReportIntegrationTester()

    try:
        success = tester.run_complete_integration_test()

        if success:
            print("\n🎉 VirtualBox to report integration test passed!")
            return 0
        else:
            print("\n❌ VirtualBox to report integration test failed!")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        tester.cleanup_resources()
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        tester.cleanup_resources()
        return 1


if __name__ == "__main__":
    exit(main())
