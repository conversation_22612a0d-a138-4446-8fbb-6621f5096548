{"builders": [{"accelerator": "kvm", "boot_wait": "2s", "communicator": "winrm", "disk_cache": "unsafe", "disk_interface": "virtio", "disk_size": "40000M", "cd_files": ["./files/virtio-drivers/*"], "cd_label": "cidata", "floppy_files": ["config/Autounattend.xml", "files/virtio-drivers/NetKVM/w10/amd64/*.cat", "files/virtio-drivers/NetKVM/w10/amd64/*.inf", "files/virtio-drivers/NetKVM/w10/amd64/*.sys", "files/virtio-drivers/vioscsi/w10/amd64/*.cat", "files/virtio-drivers/vioscsi/w10/amd64/*.inf", "files/virtio-drivers/vioscsi/w10/amd64/*.sys", "files/virtio-drivers/viostor/w10/amd64/*.cat", "files/virtio-drivers/viostor/w10/amd64/*.inf", "files/virtio-drivers/viostor/w10/amd64/*.sys", "files/virtio-drivers/Balloon/w10/amd64/*.inf", "files/virtio-drivers/Balloon/w10/amd64/*.sys", "files/virtio-drivers/Balloon/w10/amd64/*.cat", "files/virtio-drivers/qemupciserial/w10/amd64/*.cat", "files/virtio-drivers/qemupciserial/w10/amd64/*.inf", "files/virtio-drivers/qemupciserial/w10/amd64/*.sys", "files/virtio-drivers/vioserial/w10/amd64/vioser.sys", "files/virtio-drivers/vioserial/w10/amd64/vioser.inf", "files/virtio-drivers/vioserial/w10/amd64/vioser.cat", "scripts/configure-winrm.ps1", "scripts/update-windows.ps1"], "format": "qcow2", "headless": "false", "host_port_max": "6985", "host_port_min": "5985", "iso_checksum": "sha256:{{user `iso_sha256`}}", "iso_url": "{{user `iso_path`}}", "net_device": "virtio-net", "machine_type": "q35", "qemuargs": [["-m", "4096M"], ["-cpu", "qemu64,+ssse3,+sse4.1,+sse4.2"], ["-smp", "4"]], "shutdown_command": "shutdown /s /t 5 /f /d p:4:1 /c \"Packer Shutdown\"", "skip_compaction": false, "type": "qemu", "vm_name": "win-10", "winrm_insecure": "true", "winrm_password": "vagrant", "winrm_timeout": "12h", "winrm_use_ssl": "false", "winrm_username": "vagrant"}], "post-processors": [{"keep_input_artifact": false, "output": "../../vagrant_templates/win10-turdparty/windows10-turdparty.box", "type": "vagrant", "vagrantfile_template": "turdparty-vagrantfile.template"}], "provisioners": [{"type": "windows-update"}, {"destination": "C:\\qemu-ga-x86_64.msi", "source": "files/virtio-drivers/guest-agent/qemu-ga-x86_64.msi", "type": "file"}, {"destination": "C:\\blnsvr.pdb", "source": "files/virtio-drivers/Balloon/w10/amd64/blnsvr.pdb", "type": "file"}, {"destination": "C:\\blnsvr.exe", "source": "files/virtio-drivers/Balloon/w10/amd64/blnsvr.exe", "type": "file"}, {"destination": "C:\\balloon.pdb", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.pdb", "type": "file"}, {"destination": "C:\\balloon.inf", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.inf", "type": "file"}, {"destination": "C:\\balloon.sys", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.sys", "type": "file"}, {"destination": "C:\\balloon.cat", "source": "files/virtio-drivers/Balloon/w10/amd64/balloon.cat", "type": "file"}, {"destination": "C:\\vioser.sys", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.sys", "type": "file"}, {"destination": "C:\\vioser.inf", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.inf", "type": "file"}, {"destination": "C:\\vioser.cat", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.cat", "type": "file"}, {"destination": "C:\\vioser.pdb", "source": "files/virtio-drivers/vioserial/w10/amd64/vioser.pdb", "type": "file"}, {"destination": "C:\\temp\\fibratus-turdparty.yml", "source": "files/fibratus-turdparty.yml", "type": "file"}, {"destination": "C:\\temp\\configure-fibratus.ps1", "source": "files/configure-fibratus.ps1", "type": "file"}, {"elevated_password": "vagrant", "elevated_user": "vagrant", "scripts": ["scripts/install-guest-tools.ps1", "scripts/enable-rdp.ps1", "scripts/disable-hibernate.ps1", "scripts/disable-autologin.ps1", "scripts/enable-uac.ps1", "scripts/no-expiration.ps1", "scripts/install-10btooling.ps1"], "type": "powershell"}, {"restart_check_command": "powershell -command \"& {if ((get-content C:\\ProgramData\\lastboot.txt) -eq (Get-WmiObject win32_operatingsystem).LastBootUpTime) {Write-Output 'Sleeping for 600 seconds to wait for reboot'; start-sleep 600} else {Write-Output 'Reboot complete'}}\"", "restart_command": "powershell \"& {(Get-WmiObject win32_operatingsystem).LastBootUpTime > C:\\ProgramData\\lastboot.txt; Restart-Computer -force}\"", "type": "windows-restart"}], "variables": {"build_uuid": "", "iso_sha256": "", "iso_path": "", "out_path": "", "switch_name": "<PERSON><PERSON><PERSON>"}}