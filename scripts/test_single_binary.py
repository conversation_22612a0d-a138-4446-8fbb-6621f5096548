#!/usr/bin/env python3
"""
Test single binary with our gRPC integration to verify the updated script works.
"""

import importlib.util
import os
import sys

# Load the test-top-10-binaries module
spec = importlib.util.spec_from_file_location(
    "test_top_10_binaries",
    os.path.join(os.path.dirname(__file__), "test-top-10-binaries.py"),
)
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
Top20BinariesTester = test_module.Top20BinariesTester


def test_single_binary():
    """Test a single binary to verify gRPC integration."""
    print("🧪 Testing single binary with gRPC integration...")

    tester = Top20BinariesTester()

    # Test with VLC (smaller, reliable binary)
    binary_name = "vlc"
    binary_info = tester.top_20_binaries.get(binary_name)

    if not binary_info:
        print(f"❌ Binary {binary_name} not found in configuration")
        return False

    try:
        print(f"🎯 Testing {binary_name} analysis...")
        result = tester.analyze_real_binary_on_vm(binary_name, binary_info)

        print("\n📊 Test Result:")
        print(f"   Binary: {binary_name}")
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   Error: {result.get('error', 'None')}")

        if result.get("status") == "completed":
            print("✅ Single binary test PASSED!")
            return True
        else:
            print("⚠️ Single binary test completed with issues")
            return False

    except Exception as e:
        print(f"❌ Single binary test FAILED: {e}")
        import traceback

        traceback.print_exc()
        return False
    finally:
        tester.cleanup_resources()


if __name__ == "__main__":
    success = test_single_binary()
    exit(0 if success else 1)
