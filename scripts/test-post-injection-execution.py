#!/usr/bin/env python3
"""
Test Post-Injection Execution for Node.js VM

This script tests whether we can manually execute the Node.js installer
that was already injected into the VM to verify that post-injection
execution is the missing piece.
"""

import json
from pathlib import Path
import sys
import time

import requests

# Add utils to path for ServiceURLManager
sys.path.insert(0, str(Path(__file__).parent.parent / "utils"))
from service_urls import ServiceURLManager


def test_post_injection_execution():
    """Test manual execution of injected Node.js installer."""

    print("🧪 💩🎉TurdParty🎉💩 Post-Injection Execution Test")
    print("=" * 60)

    # Use ServiceURLManager for proper Traefik routing
    url_manager = ServiceURLManager("development")
    api_base = url_manager.get_service_url("api") + "/api/v1"

    # Node.js VM that has the file injected but not executed
    vm_id = "98ae8d3a-dc81-4d39-acd1-831d418f9bed"
    injected_file = "C:\\TurdParty\\node-v20.10.0-x64.msi"

    print(f"🎯 Target VM: {vm_id}")
    print(f"📁 Injected File: {injected_file}")

    # Step 1: Check VM status
    print("\n📋 Step 1: Checking VM status...")
    try:
        response = requests.get(f"{api_base}/vms/{vm_id}", timeout=30)
        if response.status_code == 200:
            vm_data = response.json()
            print(f"    ✅ VM Status: {vm_data.get('status', 'unknown')}")
            print(f"    🌐 IP Address: {vm_data.get('ip_address', 'unknown')}")
            print(
                f"    💉 Injection Completed: {vm_data.get('injection_completed', False)}"
            )
        else:
            print(f"    ❌ Failed to get VM status: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ Error checking VM: {e}")
        return False

    # Step 2: Try to execute the injected file via gRPC
    print("\n⚙️ Step 2: Attempting to execute injected Node.js installer...")

    # Try using the gRPC service directly (port 40000)
    grpc_url = "http://localhost:40000"

    execution_config = {
        "vm_id": vm_id,
        "command": f'msiexec /i "{injected_file}" /quiet /norestart',
        "working_directory": "C:\\TurdParty",
        "timeout_seconds": 300,
    }

    try:
        print(f"    🔧 Executing: {execution_config['command']}")
        response = requests.post(
            f"{grpc_url}/execute",
            json=execution_config,
            timeout=360,
            headers={"Content-Type": "application/json"},
        )

        if response.status_code == 200:
            result = response.json()
            print("    ✅ Execution successful!")
            print(f"    📤 Return Code: {result.get('return_code', 'unknown')}")
            print(f"    📝 Output: {result.get('stdout', 'No output')[:200]}...")

            # Wait a bit for events to be generated
            print("\n⏳ Step 3: Waiting 60 seconds for ECS events...")
            time.sleep(60)

            # Check for new ECS events
            return check_ecs_events_after_execution(vm_id)

        else:
            print(f"    ❌ Execution failed: {response.status_code}")
            print(f"    📝 Response: {response.text}")
            return False

    except Exception as e:
        print(f"    ❌ Error executing command: {e}")
        return False


def check_ecs_events_after_execution(vm_id):
    """Check if ECS events were generated after manual execution."""

    print("\n🔍 Step 4: Checking for new ECS events...")

    elasticsearch_url = "http://elasticsearch.turdparty.localhost"

    # Search for recent events from this VM
    search_query = {
        "query": {
            "bool": {
                "must": [
                    {"match": {"vm_id": vm_id}},
                    {"range": {"@timestamp": {"gte": "now-5m"}}},  # Last 5 minutes
                ]
            }
        },
        "size": 10,
        "sort": [{"@timestamp": {"order": "desc"}}],
    }

    try:
        response = requests.post(
            f"{elasticsearch_url}/turdparty-*/_search",
            headers={"Content-Type": "application/json"},
            json=search_query,
            timeout=30,
        )

        if response.status_code == 200:
            data = response.json()
            total_events = data.get("hits", {}).get("total", {}).get("value", 0)

            print(f"    📊 New ECS events found: {total_events}")

            if total_events > 0:
                print("    ✅ SUCCESS: Post-injection execution generated ECS events!")

                # Show sample events
                for i, hit in enumerate(data.get("hits", {}).get("hits", [])[:3]):
                    source = hit.get("_source", {})
                    timestamp = source.get("@timestamp", "unknown")
                    event_action = source.get("event", {}).get("action", "unknown")
                    print(f"    📋 Event {i+1}: {timestamp} - {event_action}")

                return True
            else:
                print(
                    "    ⚠️ No new ECS events found - execution may not have triggered monitoring"
                )
                return False
        else:
            print(f"    ❌ Failed to query Elasticsearch: {response.status_code}")
            return False

    except Exception as e:
        print(f"    ❌ Error checking ECS events: {e}")
        return False


def main():
    """Main test function."""

    success = test_post_injection_execution()

    print(f"\n{'='*60}")
    if success:
        print("🎉 POST-INJECTION EXECUTION TEST: SUCCESS")
        print("✅ Manual execution of injected file generated ECS events")
        print(
            "💡 Root cause confirmed: Celery workers not processing post-injection tasks"
        )
        print("🔧 Solution: Fix Celery worker module imports and restart workers")
    else:
        print("❌ POST-INJECTION EXECUTION TEST: FAILED")
        print("⚠️ Manual execution did not generate expected ECS events")
        print("🔍 Further investigation needed into VM monitoring setup")

    print(f"{'='*60}")

    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
