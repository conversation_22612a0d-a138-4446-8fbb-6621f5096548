#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 VirtualBox gRPC Integration Test

Comprehensive test script for VirtualBox + Vagrant + gRPC integration.
This script validates the complete infrastructure for Windows VM analysis
via gRPC communication on host port 40000.

Usage:
    python scripts/test-virtualbox-grpc-integration.py
    python scripts/test-virtualbox-grpc-integration.py --build-vm
    python scripts/test-virtualbox-grpc-integration.py --winamp-demo
"""

import argparse
import asyncio
import json
from pathlib import Path
import subprocess
import sys
import time

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class VirtualBoxGRPCIntegrationTester:
    """VirtualBox gRPC integration tester."""

    def __init__(self):
        self.console = Console()
        self.test_results = {}

    def print_header(self):
        """Print test header."""
        header_panel = Panel(
            "🖥️ [bold magenta]💩🎉TurdParty🎉💩 VirtualBox gRPC Integration[/bold magenta] 🖥️\n\n"
            "🎯 Comprehensive VirtualBox + Vagrant + gRPC Testing\n"
            "🔗 gRPC Communication: localhost:40000\n"
            "🖥️ VM Provider: VirtualBox with Windows Support\n"
            "📊 Integration: TurdParty API + Vagrant + gRPC + Windows Analysis",
            title="VirtualBox gRPC Integration Test",
            border_style="magenta",
        )
        self.console.print(header_panel)
        self.console.print()

    def test_virtualbox_installation(self) -> bool:
        """Test VirtualBox installation."""
        self.console.print(
            "[bold blue]🔍 Testing VirtualBox Installation...[/bold blue]"
        )

        try:
            result = subprocess.run(
                ["VBoxManage", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
                check=False,
            )

            if result.returncode == 0:
                version = result.stdout.strip()
                self.console.print(f"[green]✅ VirtualBox installed: {version}[/green]")

                # Check for existing VMs
                vm_result = subprocess.run(
                    ["VBoxManage", "list", "vms"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=False,
                )

                if vm_result.returncode == 0:
                    vm_count = (
                        len(vm_result.stdout.strip().split("\n"))
                        if vm_result.stdout.strip()
                        else 0
                    )
                    self.console.print(f"[cyan]📊 Existing VMs: {vm_count}[/cyan]")

                self.test_results["virtualbox"] = {
                    "status": "success",
                    "version": version,
                    "vm_count": vm_count,
                }
                return True
            else:
                self.console.print("[red]❌ VirtualBox not working properly[/red]")
                self.test_results["virtualbox"] = {
                    "status": "failed",
                    "error": "VirtualBox not working",
                }
                return False

        except Exception as e:
            self.console.print(f"[red]❌ VirtualBox test failed: {e}[/red]")
            self.test_results["virtualbox"] = {"status": "error", "error": str(e)}
            return False

    def test_vagrant_configuration(self) -> bool:
        """Test Vagrant configuration."""
        self.console.print("[bold blue]🔍 Testing Vagrant Configuration...[/bold blue]")

        try:
            # Check Vagrant version
            result = subprocess.run(
                ["vagrant", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
                check=False,
            )

            if result.returncode == 0:
                version = result.stdout.strip()
                self.console.print(f"[green]✅ Vagrant installed: {version}[/green]")

                # Check Vagrant plugins
                plugin_result = subprocess.run(
                    ["vagrant", "plugin", "list"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=False,
                )

                if plugin_result.returncode == 0:
                    plugins = plugin_result.stdout.strip().split("\n")
                    self.console.print(
                        f"[cyan]🔌 Vagrant plugins: {len(plugins)}[/cyan]"
                    )

                    # Check for VirtualBox support
                    has_vbox_support = any("vbguest" in plugin for plugin in plugins)
                    if has_vbox_support:
                        self.console.print(
                            "[green]✅ VirtualBox guest additions support available[/green]"
                        )
                    else:
                        self.console.print(
                            "[yellow]⚠️ VirtualBox guest additions plugin not found[/yellow]"
                        )

                # Check available boxes
                box_result = subprocess.run(
                    ["vagrant", "box", "list"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=False,
                )

                if box_result.returncode == 0:
                    boxes = (
                        box_result.stdout.strip().split("\n")
                        if box_result.stdout.strip()
                        else []
                    )
                    box_count = len(boxes)
                    self.console.print(f"[cyan]📦 Available boxes: {box_count}[/cyan]")

                    # Check for Windows boxes
                    windows_boxes = [box for box in boxes if "windows" in box.lower()]
                    if windows_boxes:
                        self.console.print(
                            f"[green]✅ Windows boxes available: {len(windows_boxes)}[/green]"
                        )
                        for box in windows_boxes:
                            self.console.print(f"[cyan]  - {box}[/cyan]")
                    else:
                        self.console.print("[yellow]⚠️ No Windows boxes found[/yellow]")

                self.test_results["vagrant"] = {
                    "status": "success",
                    "version": version,
                    "plugins": len(plugins),
                    "boxes": box_count,
                    "windows_boxes": len(windows_boxes)
                    if "windows_boxes" in locals()
                    else 0,
                }
                return True
            else:
                self.console.print("[red]❌ Vagrant not working properly[/red]")
                self.test_results["vagrant"] = {
                    "status": "failed",
                    "error": "Vagrant not working",
                }
                return False

        except Exception as e:
            self.console.print(f"[red]❌ Vagrant test failed: {e}[/red]")
            self.test_results["vagrant"] = {"status": "error", "error": str(e)}
            return False

    def test_grpc_connectivity(self) -> bool:
        """Test gRPC connectivity."""
        self.console.print("[bold blue]🔍 Testing gRPC Connectivity...[/bold blue]")

        try:
            # Run the existing gRPC connectivity test
            result = subprocess.run(
                [sys.executable, "scripts/test-vagrant-grpc-connectivity.py"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=project_root,
                check=False,
            )

            success = result.returncode == 0 and "READY FOR GRPC" in result.stdout

            if success:
                self.console.print("[green]✅ gRPC connectivity verified[/green]")
                self.console.print("[cyan]🔗 Port 40000: Accessible[/cyan]")
                self.test_results["grpc"] = {"status": "success", "port": 40000}
                return True
            else:
                self.console.print("[red]❌ gRPC connectivity failed[/red]")
                self.console.print(f"[yellow]Output: {result.stdout[-200:]}[/yellow]")
                self.test_results["grpc"] = {
                    "status": "failed",
                    "error": "Connectivity test failed",
                }
                return False

        except Exception as e:
            self.console.print(f"[red]❌ gRPC test failed: {e}[/red]")
            self.test_results["grpc"] = {"status": "error", "error": str(e)}
            return False

    def test_turdparty_api(self) -> bool:
        """Test TurdParty API availability."""
        self.console.print("[bold blue]🔍 Testing TurdParty API...[/bold blue]")

        try:
            import requests

            # Test API health
            response = requests.get("http://localhost:8000/health", timeout=10)

            if response.status_code == 200:
                self.console.print("[green]✅ TurdParty API available[/green]")

                # Test VM endpoints
                vm_response = requests.get(
                    "http://localhost:8000/api/v1/vms/", timeout=10
                )
                if vm_response.status_code == 200:
                    self.console.print(
                        "[green]✅ VM management endpoints available[/green]"
                    )

                self.test_results["api"] = {"status": "success", "health": "ok"}
                return True
            else:
                self.console.print(
                    f"[red]❌ TurdParty API not available: {response.status_code}[/red]"
                )
                self.test_results["api"] = {
                    "status": "failed",
                    "error": f"HTTP {response.status_code}",
                }
                return False

        except Exception as e:
            self.console.print(f"[red]❌ API test failed: {e}[/red]")
            self.test_results["api"] = {"status": "error", "error": str(e)}
            return False

    def test_winamp_demo(self) -> bool:
        """Test Winamp gRPC demo."""
        self.console.print("[bold blue]🎵 Testing Winamp gRPC Demo...[/bold blue]")

        try:
            result = subprocess.run(
                [sys.executable, "scripts/run-winamp-grpc-demo.py"],
                capture_output=True,
                text=True,
                timeout=300,
                cwd=project_root,
                check=False,
            )

            success = (
                result.returncode == 0 and "completed successfully" in result.stdout
            )

            if success:
                self.console.print("[green]✅ Winamp gRPC demo successful[/green]")
                self.test_results["winamp_demo"] = {
                    "status": "success",
                    "demo": "completed",
                }
                return True
            else:
                self.console.print("[red]❌ Winamp gRPC demo failed[/red]")
                self.console.print(f"[yellow]Error: {result.stderr[-200:]}[/yellow]")
                self.test_results["winamp_demo"] = {
                    "status": "failed",
                    "error": "Demo failed",
                }
                return False

        except Exception as e:
            self.console.print(f"[red]❌ Winamp demo test failed: {e}[/red]")
            self.test_results["winamp_demo"] = {"status": "error", "error": str(e)}
            return False

    def check_windows_vm_requirements(self) -> bool:
        """Check Windows VM build requirements."""
        self.console.print(
            "[bold blue]🔍 Checking Windows VM Requirements...[/bold blue]"
        )

        try:
            # Check for Windows ISOs
            iso_dir = project_root / "isos"
            if iso_dir.exists():
                iso_files = list(iso_dir.glob("*.iso"))
                if iso_files:
                    self.console.print(
                        f"[green]✅ Windows ISOs available: {len(iso_files)}[/green]"
                    )
                    for iso in iso_files:
                        self.console.print(f"[cyan]  - {iso.name}[/cyan]")
                else:
                    self.console.print(
                        "[yellow]⚠️ No Windows ISOs found in isos/[/yellow]"
                    )

            # Check for Packer templates
            packer_dir = project_root / "vendor/10b/packer-templates/windows"
            if packer_dir.exists():
                self.console.print(
                    "[green]✅ Packer Windows templates available[/green]"
                )
            else:
                self.console.print(
                    "[yellow]⚠️ Packer Windows templates not found[/yellow]"
                )

            # Check for Vagrant templates
            vagrant_dir = project_root / "vagrant_templates"
            if vagrant_dir.exists():
                self.console.print(
                    "[green]✅ Vagrant template directory exists[/green]"
                )
            else:
                self.console.print(
                    "[yellow]⚠️ Vagrant template directory not found[/yellow]"
                )

            self.test_results["windows_vm_requirements"] = {
                "status": "checked",
                "isos_available": len(iso_files) if "iso_files" in locals() else 0,
                "packer_templates": packer_dir.exists(),
                "vagrant_templates": vagrant_dir.exists(),
            }
            return True

        except Exception as e:
            self.console.print(
                f"[red]❌ Windows VM requirements check failed: {e}[/red]"
            )
            self.test_results["windows_vm_requirements"] = {
                "status": "error",
                "error": str(e),
            }
            return False

    def generate_integration_report(self):
        """Generate comprehensive integration report."""
        self.console.print(
            "\n[bold green]📊 VirtualBox gRPC Integration Report[/bold green]"
        )

        # Summary table
        table = Table(title="🖥️ Integration Test Results")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="bold")
        table.add_column("Details", style="white")

        for component, result in self.test_results.items():
            status = result.get("status", "unknown")

            if status == "success":
                status_display = "[green]✅ SUCCESS[/green]"
                details = result.get("version", result.get("demo", "OK"))
            elif status == "failed":
                status_display = "[red]❌ FAILED[/red]"
                details = result.get("error", "Failed")
            elif status == "error":
                status_display = "[red]🔥 ERROR[/red]"
                details = result.get("error", "Error")
            else:
                status_display = "[yellow]❓ UNKNOWN[/yellow]"
                details = "Unknown status"

            table.add_row(
                component.replace("_", " ").title(),
                status_display,
                str(details)[:50] + "..." if len(str(details)) > 50 else str(details),
            )

        self.console.print(table)

        # Overall assessment
        successful_tests = sum(
            1
            for result in self.test_results.values()
            if result.get("status") == "success"
        )
        total_tests = len(self.test_results)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0

        if success_rate >= 80:
            overall_status = "[green]🎉 EXCELLENT[/green]"
            border_style = "green"
        elif success_rate >= 60:
            overall_status = "[yellow]⚠️ GOOD[/yellow]"
            border_style = "yellow"
        else:
            overall_status = "[red]❌ NEEDS WORK[/red]"
            border_style = "red"

        summary_panel = Panel(
            f"📊 [bold]Integration Assessment[/bold]\n\n"
            f"Successful Tests: {successful_tests}/{total_tests}\n"
            f"Success Rate: {success_rate:.1f}%\n"
            f"Overall Status: {overall_status}\n\n"
            f"🔗 gRPC Port 40000: {'✅ Ready' if self.test_results.get('grpc', {}).get('status') == 'success' else '❌ Not Ready'}\n"
            f"🖥️ VirtualBox: {'✅ Available' if self.test_results.get('virtualbox', {}).get('status') == 'success' else '❌ Not Available'}\n"
            f"🏠 Vagrant: {'✅ Configured' if self.test_results.get('vagrant', {}).get('status') == 'success' else '❌ Not Configured'}\n"
            f"🎵 Winamp Demo: {'✅ Working' if self.test_results.get('winamp_demo', {}).get('status') == 'success' else '❌ Not Working'}",
            title="VirtualBox gRPC Integration Status",
            border_style=border_style,
        )
        self.console.print(summary_panel)

        # Save detailed report
        report_file = f"/tmp/virtualbox_grpc_integration_report_{int(time.time())}.json"
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S UTC", time.gmtime()),
            "integration_test": "VirtualBox gRPC Integration",
            "success_rate": success_rate,
            "overall_status": "excellent"
            if success_rate >= 80
            else "good"
            if success_rate >= 60
            else "needs_work",
            "test_results": self.test_results,
            "recommendations": self._generate_recommendations(),
        }

        with open(report_file, "w") as f:
            json.dump(report_data, f, indent=2)

        self.console.print(f"[cyan]📄 Detailed report saved to: {report_file}[/cyan]")

        return success_rate >= 60

    def _generate_recommendations(self) -> list:
        """Generate recommendations based on test results."""
        recommendations = []

        if self.test_results.get("virtualbox", {}).get("status") != "success":
            recommendations.append("Install or fix VirtualBox installation")

        if self.test_results.get("vagrant", {}).get("status") != "success":
            recommendations.append("Install or configure Vagrant properly")

        if self.test_results.get("grpc", {}).get("status") != "success":
            recommendations.append("Fix gRPC connectivity on port 40000")

        if self.test_results.get("api", {}).get("status") != "success":
            recommendations.append("Start TurdParty API services")

        if self.test_results.get("winamp_demo", {}).get("status") != "success":
            recommendations.append("Debug Winamp gRPC demo issues")

        windows_vm_req = self.test_results.get("windows_vm_requirements", {})
        if windows_vm_req.get("isos_available", 0) == 0:
            recommendations.append("Add Windows ISO files to isos/ directory")

        if not recommendations:
            recommendations.append(
                "All components working well - ready for Windows VM integration!"
            )

        return recommendations

    async def run_complete_integration_test(self, args) -> bool:
        """Run complete integration test."""
        start_time = time.time()

        self.print_header()

        try:
            # Run all tests
            self.test_virtualbox_installation()
            self.test_vagrant_configuration()
            self.test_grpc_connectivity()
            self.test_turdparty_api()
            self.check_windows_vm_requirements()

            if args.winamp_demo:
                self.test_winamp_demo()

            # Generate report
            total_time = time.time() - start_time
            self.console.print(
                f"\n⏱️ Total integration test time: {total_time:.2f} seconds"
            )

            return self.generate_integration_report()

        except Exception as e:
            self.console.print(f"[red]❌ Integration test error: {e}[/red]")
            return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="💩🎉TurdParty🎉💩 VirtualBox gRPC Integration Test"
    )
    parser.add_argument(
        "--winamp-demo", action="store_true", help="Include Winamp gRPC demo test"
    )
    parser.add_argument(
        "--build-vm",
        action="store_true",
        help="Build Windows VM after tests (not implemented yet)",
    )

    args = parser.parse_args()

    # Run the integration test
    tester = VirtualBoxGRPCIntegrationTester()

    try:
        success = asyncio.run(tester.run_complete_integration_test(args))

        if success:
            print("\n🎉 VirtualBox gRPC integration test completed successfully!")
            return 0
        else:
            print("\n❌ VirtualBox gRPC integration test failed!")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ Integration test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
