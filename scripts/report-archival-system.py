#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Report Archival System

Comprehensive system for organizing, archiving, and managing analysis reports.
Ensures all reports are kept separate with proper historical tracking.
"""

from datetime import UTC, datetime, timezone
import json
from pathlib import Path
import shutil
from typing import Any, Dict, List
import uuid


class ReportArchivalSystem:
    """Manages report archival, organization, and historical tracking."""

    def __init__(self, base_reports_dir: str = "docs/analysis-reports"):
        self.base_dir = Path(base_reports_dir)
        self.archive_dir = self.base_dir / "archive"
        self.metadata_file = self.base_dir / "reports-metadata.json"

        # Create directory structure
        self.base_dir.mkdir(exist_ok=True)
        self.archive_dir.mkdir(exist_ok=True)

        # Load or create metadata
        self.metadata = self._load_metadata()

    def _load_metadata(self) -> dict[str, Any]:
        """Load reports metadata or create new structure."""
        if self.metadata_file.exists():
            with open(self.metadata_file) as f:
                return json.load(f)
        else:
            return {
                "version": "1.0",
                "created": datetime.now(UTC).isoformat(),
                "reports": {},
                "statistics": {
                    "total_reports": 0,
                    "reports_by_type": {},
                    "reports_by_date": {},
                },
            }

    def _save_metadata(self):
        """Save metadata to file."""
        with open(self.metadata_file, "w") as f:
            json.dump(self.metadata, f, indent=2, default=str)

    def generate_unique_report_path(
        self, binary_name: str, report_type: str = "analysis"
    ) -> tuple[Path, str]:
        """Generate unique report path with timestamp and UUID."""

        # Create date-based directory
        date_str = datetime.now(UTC).strftime("%Y-%m-%d")
        date_dir = self.base_dir / date_str
        date_dir.mkdir(exist_ok=True)

        # Generate unique filename
        timestamp = datetime.now(UTC).strftime("%H%M%S")
        short_uuid = str(uuid.uuid4())[:8]

        # Clean binary name for filename
        clean_binary_name = "".join(
            c for c in binary_name if c.isalnum() or c in "-_"
        ).lower()
        filename = f"{clean_binary_name}-{report_type}-{timestamp}-{short_uuid}.rst"

        report_path = date_dir / filename
        report_id = f"{date_str}_{timestamp}_{short_uuid}"

        return report_path, report_id

    def register_report(
        self, report_path: Path, report_id: str, metadata: dict[str, Any]
    ):
        """Register a new report in the archival system."""

        report_info = {
            "report_id": report_id,
            "file_path": str(report_path.relative_to(self.base_dir)),
            "binary_name": metadata.get("binary_name", "unknown"),
            "file_uuid": metadata.get("file_uuid", ""),
            "report_type": metadata.get("report_type", "analysis"),
            "generated_at": datetime.now(UTC).isoformat(),
            "file_size_bytes": report_path.stat().st_size
            if report_path.exists()
            else 0,
            "ecs_events_count": metadata.get("ecs_events_count", 0),
            "risk_level": metadata.get("risk_level", "unknown"),
            "analysis_status": metadata.get("analysis_status", "complete"),
        }

        # Add to metadata
        self.metadata["reports"][report_id] = report_info

        # Update statistics
        self._update_statistics(report_info)

        # Save metadata
        self._save_metadata()

        print(f"    ✅ Report registered: {report_id}")
        print(f"    📁 Path: {report_info['file_path']}")

    def _update_statistics(self, report_info: dict[str, Any]):
        """Update archival statistics."""
        stats = self.metadata["statistics"]

        # Total reports
        stats["total_reports"] += 1

        # By type
        report_type = report_info["report_type"]
        if report_type not in stats["reports_by_type"]:
            stats["reports_by_type"][report_type] = 0
        stats["reports_by_type"][report_type] += 1

        # By date
        date_str = report_info["generated_at"][:10]  # YYYY-MM-DD
        if date_str not in stats["reports_by_date"]:
            stats["reports_by_date"][date_str] = 0
        stats["reports_by_date"][date_str] += 1

    def list_reports(
        self, report_type: str = None, date_filter: str = None
    ) -> list[dict[str, Any]]:
        """List reports with optional filtering."""
        reports = []

        for report_id, report_info in self.metadata["reports"].items():
            # Apply filters
            if report_type and report_info["report_type"] != report_type:
                continue
            if date_filter and not report_info["generated_at"].startswith(date_filter):
                continue

            reports.append(report_info)

        # Sort by generation time (newest first)
        reports.sort(key=lambda x: x["generated_at"], reverse=True)
        return reports

    def archive_old_reports(self, days_old: int = 30):
        """Archive reports older than specified days."""
        cutoff_date = datetime.now(UTC).timestamp() - (days_old * 24 * 60 * 60)
        archived_count = 0

        for report_id, report_info in self.metadata["reports"].items():
            report_date = datetime.fromisoformat(
                report_info["generated_at"].replace("Z", "+00:00")
            )

            if report_date.timestamp() < cutoff_date:
                # Move to archive
                current_path = self.base_dir / report_info["file_path"]
                if current_path.exists():
                    archive_path = self.archive_dir / current_path.name
                    shutil.move(str(current_path), str(archive_path))

                    # Update metadata
                    report_info["archived"] = True
                    report_info["archive_path"] = str(
                        archive_path.relative_to(self.base_dir)
                    )
                    archived_count += 1

        if archived_count > 0:
            self._save_metadata()
            print(
                f"    📦 Archived {archived_count} reports older than {days_old} days"
            )

        return archived_count

    def generate_reports_summary(self) -> str:
        """Generate a summary of all reports in the system."""
        stats = self.metadata["statistics"]

        summary = f"""
💩🎉TurdParty🎉💩 Reports Archive Summary
========================================

**Total Reports**: {stats['total_reports']}
**Archive Created**: {self.metadata['created'][:10]}
**Last Updated**: {datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}

Reports by Type:
"""

        for report_type, count in stats["reports_by_type"].items():
            summary += f"- **{report_type.title()}**: {count} reports\n"

        summary += "\nRecent Reports (Last 5):\n"

        recent_reports = self.list_reports()[:5]
        for report in recent_reports:
            summary += f"- {report['binary_name']} ({report['generated_at'][:16]})\n"

        return summary

    def cleanup_empty_directories(self):
        """Remove empty date directories."""
        for date_dir in self.base_dir.iterdir():
            if (
                date_dir.is_dir()
                and date_dir.name != "archive"
                and not any(date_dir.iterdir())
            ):
                date_dir.rmdir()
                print(f"    🗑️ Removed empty directory: {date_dir.name}")


def main():
    """Test the archival system."""
    print("🗄️ 💩🎉TurdParty🎉💩 Report Archival System Test")
    print("=" * 60)

    archival = ReportArchivalSystem()

    # Generate summary
    summary = archival.generate_reports_summary()
    print(summary)

    # Test unique path generation
    print("\n🔧 Testing unique path generation...")
    path1, id1 = archival.generate_unique_report_path("node-v20.10.0-x64.msi", "nodejs")
    path2, id2 = archival.generate_unique_report_path("node-v20.10.0-x64.msi", "nodejs")

    print(f"    📁 Path 1: {path1}")
    print(f"    📁 Path 2: {path2}")
    print(f"    ✅ Paths are unique: {path1 != path2}")

    # List recent reports
    print("\n📋 Recent Reports:")
    recent = archival.list_reports()[:3]
    for report in recent:
        print(f"    - {report['binary_name']} ({report['report_id']})")

    print("\n✅ Archival system test complete!")


if __name__ == "__main__":
    main()
