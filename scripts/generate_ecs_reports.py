#!/usr/bin/env python3
"""
Generate reports using the available ECS API endpoints for our successful analyses.
"""

from datetime import datetime
import json
import subprocess

# File IDs from our successful test run
successful_analyses = {
    "vscode": "2d8bde4c-2f18-4d75-8778-e07b6c68f59c",
    "nodejs": "a35065f9-f46e-47ea-9c1c-089d18ab0e99",
    "python": "63b0c18f-72b3-4a78-af88-5a23e4da72dd",
    "chrome": "2a26b9f3-10d6-400e-bc65-8638fafab83f",
    "firefox": "653579f8-4bbb-437b-bc69-1bd51aa72f45",
    "git": "cae3bc4a-c11a-432f-8486-8cad034a7491",
    "notepadpp": "7023282c-e56e-4f29-b6f8-aa5325f5a9a1",
    "7zip": "bc5943fe-ed20-4702-8b01-0974c6db95e2",
    "putty": "74a7373d-b740-49a5-bcf0-ff6cdb8560e5",
    "vlc": "2e674e94-ca21-4769-8e9b-66810933fca6",
}

# VM IDs from our test run (extracted from terminal output)
vm_mappings = {
    "vscode": "bacfd4ca-1aab-4e1c-87e1-c11ae7ae25a2",
    "nodejs": "de7302af-4182-4961-86f0-97c8548af369",
    "python": "a2cc0be8-88ef-4165-acb4-d8650ee085e8",
    "chrome": "83217e1a-8695-4d54-aa86-17ac24c5fcb8",
    "firefox": "a482a351-4b5a-4b96-8194-f9957706face",
    "git": "1e21324f-8a22-4001-8bdc-fd5bb23244a6",
    "notepadpp": "fbd7932e-0d61-43fe-be31-fa8ff2376642",
    "7zip": "20ad4cee-9dd2-4e48-a2f5-49a1e3f4bba9",
    "putty": "66b5f52b-4d83-4bbe-8dd4-c2af8fe62f10",
    "vlc": "6901e520-f237-43be-b881-9c3997834fb5",
}


def get_vm_ecs_events(binary_name, vm_id):
    """Get ECS events for a specific VM using the API."""
    print(f"📊 Getting ECS events for {binary_name} (VM ID: {vm_id})")

    try:
        url = f"http://api.turdparty.localhost/api/v1/ecs/ecs/events/{vm_id}"
        cmd = ["curl", "-s", "-w", "\\n%{http_code}", url]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=60, check=False
        )

        # Split response and status code
        output_lines = result.stdout.strip().split("\n")
        status_code = output_lines[-1] if output_lines else "000"
        response_body = "\n".join(output_lines[:-1]) if len(output_lines) > 1 else ""

        if status_code == "200":
            print(f"   ✅ {binary_name}: ECS events retrieved successfully")
            try:
                events_data = json.loads(response_body)
                return {
                    "success": True,
                    "binary_name": binary_name,
                    "vm_id": vm_id,
                    "status_code": status_code,
                    "events_data": events_data,
                }
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "binary_name": binary_name,
                    "vm_id": vm_id,
                    "status_code": status_code,
                    "error": "Invalid JSON response",
                }
        else:
            print(f"   ❌ {binary_name}: ECS events failed (HTTP {status_code})")
            return {
                "success": False,
                "binary_name": binary_name,
                "vm_id": vm_id,
                "status_code": status_code,
                "error": response_body,
            }

    except Exception as e:
        print(f"   ❌ {binary_name}: Exception during ECS events retrieval - {e}")
        return {
            "success": False,
            "binary_name": binary_name,
            "vm_id": vm_id,
            "error": str(e),
        }


def get_vm_events_summary(binary_name, vm_id):
    """Get ECS events summary for a specific VM."""
    print(f"📋 Getting ECS events summary for {binary_name}")

    try:
        url = f"http://api.turdparty.localhost/api/v1/ecs/ecs/events/{vm_id}/summary"
        cmd = ["curl", "-s", "-w", "\\n%{http_code}", url]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=60, check=False
        )

        output_lines = result.stdout.strip().split("\n")
        status_code = output_lines[-1] if output_lines else "000"
        response_body = "\n".join(output_lines[:-1]) if len(output_lines) > 1 else ""

        if status_code == "200":
            print(f"   ✅ {binary_name}: ECS summary retrieved successfully")
            try:
                summary_data = json.loads(response_body)
                return {"success": True, "summary_data": summary_data}
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response"}
        else:
            print(f"   ❌ {binary_name}: ECS summary failed (HTTP {status_code})")
            return {"success": False, "error": response_body}

    except Exception as e:
        print(f"   ❌ {binary_name}: Exception during ECS summary retrieval - {e}")
        return {"success": False, "error": str(e)}


def get_file_details(binary_name, file_id):
    """Get file details from the API."""
    print(f"📁 Getting file details for {binary_name}")

    try:
        url = f"http://api.turdparty.localhost/api/v1/files/{file_id}"
        cmd = ["curl", "-s", "-w", "\\n%{http_code}", url]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=60, check=False
        )

        output_lines = result.stdout.strip().split("\n")
        status_code = output_lines[-1] if output_lines else "000"
        response_body = "\n".join(output_lines[:-1]) if len(output_lines) > 1 else ""

        if status_code == "200":
            print(f"   ✅ {binary_name}: File details retrieved successfully")
            try:
                file_data = json.loads(response_body)
                return {"success": True, "file_data": file_data}
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response"}
        else:
            print(f"   ❌ {binary_name}: File details failed (HTTP {status_code})")
            return {"success": False, "error": response_body}

    except Exception as e:
        print(f"   ❌ {binary_name}: Exception during file details retrieval - {e}")
        return {"success": False, "error": str(e)}


def generate_comprehensive_report(binary_name, file_id, vm_id):
    """Generate a comprehensive report for a binary."""
    print(f"\n📦 Generating comprehensive report for {binary_name}")

    report = {
        "binary_name": binary_name,
        "file_id": file_id,
        "vm_id": vm_id,
        "generated_at": datetime.now().isoformat(),
        "report_sections": {},
    }

    # Get file details
    file_result = get_file_details(binary_name, file_id)
    report["report_sections"]["file_details"] = file_result

    # Get ECS events
    events_result = get_vm_ecs_events(binary_name, vm_id)
    report["report_sections"]["ecs_events"] = events_result

    # Get ECS summary
    summary_result = get_vm_events_summary(binary_name, vm_id)
    report["report_sections"]["ecs_summary"] = summary_result

    # Calculate success metrics
    successful_sections = sum(
        1
        for section in report["report_sections"].values()
        if section.get("success", False)
    )
    total_sections = len(report["report_sections"])

    report["report_metadata"] = {
        "successful_sections": successful_sections,
        "total_sections": total_sections,
        "success_rate": successful_sections / total_sections * 100
        if total_sections > 0
        else 0,
        "overall_success": successful_sections == total_sections,
    }

    return report


def main():
    """Generate ECS reports for all successful analyses."""
    print("🚀 Generating ECS Reports for Top 20 Binaries Successful Analyses")
    print("=" * 80)

    all_reports = {}

    for binary_name in successful_analyses:
        if binary_name in vm_mappings:
            file_id = successful_analyses[binary_name]
            vm_id = vm_mappings[binary_name]

            report = generate_comprehensive_report(binary_name, file_id, vm_id)
            all_reports[binary_name] = report
        else:
            print(f"⚠️ No VM mapping found for {binary_name}")

    # Summary
    print(f"\n{'='*80}")
    print("📊 ECS REPORT GENERATION SUMMARY")
    print(f"{'='*80}")

    total_binaries = len(all_reports)
    successful_reports = sum(
        1
        for report in all_reports.values()
        if report["report_metadata"]["overall_success"]
    )

    print(f"📈 Total Binaries Processed: {total_binaries}")
    print(f"✅ Successful Complete Reports: {successful_reports}")
    print(f"⚠️ Partial Reports: {total_binaries - successful_reports}")
    print(f"📊 Success Rate: {successful_reports/total_binaries*100:.1f}%")

    print("\n📋 DETAILED RESULTS BY BINARY:")
    for binary_name, report in all_reports.items():
        metadata = report["report_metadata"]
        status = "✅" if metadata["overall_success"] else "⚠️"
        success_rate = metadata["success_rate"]
        print(
            f"   {status} {binary_name}: {metadata['successful_sections']}/{metadata['total_sections']} sections ({success_rate:.1f}%)"
        )

    # Save comprehensive results
    results_file = (
        f"ecs_comprehensive_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    with open(results_file, "w") as f:
        json.dump(all_reports, f, indent=2, default=str)

    print(f"\n📄 Comprehensive reports saved to: {results_file}")
    print("🎉 ECS report generation complete!")

    return all_reports


if __name__ == "__main__":
    main()
