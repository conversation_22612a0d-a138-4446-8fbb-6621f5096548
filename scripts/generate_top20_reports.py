#!/usr/bin/env python3
"""
Generate comprehensive reports for the successful Top 20 binaries analyses.
"""

from datetime import datetime
import json
import subprocess

# File IDs from our successful test run
successful_analyses = {
    "vscode": "2d8bde4c-2f18-4d75-8778-e07b6c68f59c",
    "nodejs": "a35065f9-f46e-47ea-9c1c-089d18ab0e99",
    "python": "63b0c18f-72b3-4a78-af88-5a23e4da72dd",
    "chrome": "2a26b9f3-10d6-400e-bc65-8638fafab83f",
    "firefox": "653579f8-4bbb-437b-bc69-1bd51aa72f45",
    "git": "cae3bc4a-c11a-432f-8486-8cad034a7491",
    "notepadpp": "7023282c-e56e-4f29-b6f8-aa5325f5a9a1",
    "7zip": "bc5943fe-ed20-4702-8b01-0974c6db95e2",
    "putty": "74a7373d-b740-49a5-bcf0-ff6cdb8560e5",
    "vlc": "2e674e94-ca21-4769-8e9b-66810933fca6",
}


def generate_report_for_file(binary_name, file_id):
    """Generate a report for a specific file ID."""
    print(f"📊 Generating report for {binary_name} (File ID: {file_id})")

    try:
        # Use the generic report generator
        cmd = ["python", "scripts/generate-generic-report.py", file_id]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=120, check=False
        )

        if result.returncode == 0:
            print(f"   ✅ {binary_name}: Report generated successfully")
            return {
                "success": True,
                "file_id": file_id,
                "binary_name": binary_name,
                "output": result.stdout,
            }
        else:
            print(f"   ❌ {binary_name}: Report generation failed")
            print(f"      Error: {result.stderr}")
            return {
                "success": False,
                "file_id": file_id,
                "binary_name": binary_name,
                "error": result.stderr,
            }

    except Exception as e:
        print(f"   ❌ {binary_name}: Exception during report generation - {e}")
        return {
            "success": False,
            "file_id": file_id,
            "binary_name": binary_name,
            "error": str(e),
        }


def verify_elasticsearch_entries(file_id):
    """Verify that Elasticsearch has entries for this file ID."""
    try:
        cmd = [
            "curl",
            "-s",
            f"http://elasticsearch.turdparty.localhost/turdparty-install-ecs-*/_search?q=turdparty.file_id:{file_id}&size=1",
        ]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=30, check=False
        )

        if result.returncode == 0:
            try:
                data = json.loads(result.stdout)
                hit_count = data.get("hits", {}).get("total", {}).get("value", 0)
                return hit_count > 0
            except json.JSONDecodeError:
                return False
        return False

    except Exception:
        return False


def main():
    """Generate reports for all successful analyses."""
    print("🚀 Generating Reports for Top 20 Binaries Successful Analyses")
    print("=" * 80)

    report_results = {}
    verified_count = 0

    for binary_name, file_id in successful_analyses.items():
        print(f"\n📦 Processing {binary_name}...")

        # Verify Elasticsearch entries
        has_es_entries = verify_elasticsearch_entries(file_id)
        if has_es_entries:
            print(f"   ✅ Elasticsearch entries confirmed for {file_id}")
            verified_count += 1
        else:
            print(f"   ⚠️ No Elasticsearch entries found for {file_id}")

        # Generate report
        result = generate_report_for_file(binary_name, file_id)
        result["elasticsearch_verified"] = has_es_entries
        report_results[binary_name] = result

    # Summary
    print(f"\n{'='*80}")
    print("📊 REPORT GENERATION SUMMARY")
    print(f"{'='*80}")

    successful_reports = sum(1 for r in report_results.values() if r["success"])
    total_reports = len(report_results)

    print(f"📈 Total Files Processed: {total_reports}")
    print(f"✅ Successful Reports: {successful_reports}")
    print(f"❌ Failed Reports: {total_reports - successful_reports}")
    print(f"🔍 Elasticsearch Verified: {verified_count}")
    print(f"📊 Success Rate: {successful_reports/total_reports*100:.1f}%")

    print("\n📋 DETAILED RESULTS:")
    for binary_name, result in report_results.items():
        status = "✅" if result["success"] else "❌"
        es_status = "✅" if result["elasticsearch_verified"] else "⚠️"
        print(
            f"   {status} {binary_name}: Report {'Success' if result['success'] else 'Failed'} | ES: {es_status}"
        )

    # Save results
    results_file = (
        f"top20_report_generation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    with open(results_file, "w") as f:
        json.dump(report_results, f, indent=2, default=str)

    print(f"\n📄 Results saved to: {results_file}")
    print("🎉 Report generation complete!")

    return report_results


if __name__ == "__main__":
    main()
