#!/usr/bin/env python3
"""
Generate reports using the TurdParty API reporting endpoints for our successful analyses.
"""

from datetime import datetime
import json
import subprocess

# File IDs from our successful test run
successful_analyses = {
    "vscode": "2d8bde4c-2f18-4d75-8778-e07b6c68f59c",
    "nodejs": "a35065f9-f46e-47ea-9c1c-089d18ab0e99",
    "python": "63b0c18f-72b3-4a78-af88-5a23e4da72dd",
    "chrome": "2a26b9f3-10d6-400e-bc65-8638fafab83f",
    "firefox": "653579f8-4bbb-437b-bc69-1bd51aa72f45",
    "git": "cae3bc4a-c11a-432f-8486-8cad034a7491",
    "notepadpp": "7023282c-e56e-4f29-b6f8-aa5325f5a9a1",
    "7zip": "bc5943fe-ed20-4702-8b01-0974c6db95e2",
    "putty": "74a7373d-b740-49a5-bcf0-ff6cdb8560e5",
    "vlc": "2e674e94-ca21-4769-8e9b-66810933fca6",
}


def generate_api_report(binary_name, file_id, report_type="binary"):
    """Generate a report using the TurdParty API."""
    print(f"📊 Generating {report_type} report for {binary_name} (File ID: {file_id})")

    # API endpoint for binary reports
    if report_type == "binary":
        url = f"http://api.turdparty.localhost/api/v1/reports/binary/{file_id}"
    elif report_type == "summary":
        url = f"http://api.turdparty.localhost/api/v1/reports/binary/{file_id}/summary"
    elif report_type == "footprint":
        url = (
            f"http://api.turdparty.localhost/api/v1/reports/binary/{file_id}/footprint"
        )
    elif report_type == "runtime":
        url = f"http://api.turdparty.localhost/api/v1/reports/binary/{file_id}/runtime"
    else:
        url = f"http://api.turdparty.localhost/api/v1/reports/binary/{file_id}"

    try:
        cmd = ["curl", "-s", "-w", "\\n%{http_code}", url]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=60, check=False
        )

        # Split response and status code
        output_lines = result.stdout.strip().split("\n")
        status_code = output_lines[-1] if output_lines else "000"
        response_body = "\n".join(output_lines[:-1]) if len(output_lines) > 1 else ""

        if status_code == "200":
            print(f"   ✅ {binary_name}: {report_type} report generated successfully")
            try:
                report_data = json.loads(response_body)
                return {
                    "success": True,
                    "file_id": file_id,
                    "binary_name": binary_name,
                    "report_type": report_type,
                    "status_code": status_code,
                    "report_data": report_data,
                }
            except json.JSONDecodeError:
                return {
                    "success": True,
                    "file_id": file_id,
                    "binary_name": binary_name,
                    "report_type": report_type,
                    "status_code": status_code,
                    "raw_response": response_body,
                }
        else:
            print(
                f"   ❌ {binary_name}: {report_type} report failed (HTTP {status_code})"
            )
            return {
                "success": False,
                "file_id": file_id,
                "binary_name": binary_name,
                "report_type": report_type,
                "status_code": status_code,
                "error": response_body,
            }

    except Exception as e:
        print(
            f"   ❌ {binary_name}: Exception during {report_type} report generation - {e}"
        )
        return {
            "success": False,
            "file_id": file_id,
            "binary_name": binary_name,
            "report_type": report_type,
            "error": str(e),
        }


def test_api_health():
    """Test if the reporting API is healthy."""
    print("🔍 Testing API health...")
    try:
        cmd = [
            "curl",
            "-s",
            "-w",
            "\\n%{http_code}",
            "http://api.turdparty.localhost/api/v1/reports/health",
        ]
        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=30, check=False
        )

        output_lines = result.stdout.strip().split("\n")
        status_code = output_lines[-1] if output_lines else "000"

        if status_code == "200":
            print("   ✅ Reporting API is healthy")
            return True
        else:
            print(f"   ⚠️ Reporting API health check returned HTTP {status_code}")
            return False
    except Exception as e:
        print(f"   ❌ API health check failed: {e}")
        return False


def main():
    """Generate API reports for all successful analyses."""
    print("🚀 Generating API Reports for Top 20 Binaries Successful Analyses")
    print("=" * 80)

    # Test API health first
    if not test_api_health():
        print("❌ API health check failed. Aborting report generation.")
        return

    report_results = {}

    # Generate different types of reports for each binary
    report_types = ["summary", "footprint", "runtime", "binary"]

    for binary_name, file_id in successful_analyses.items():
        print(f"\n📦 Processing {binary_name}...")

        binary_reports = {}

        for report_type in report_types:
            result = generate_api_report(binary_name, file_id, report_type)
            binary_reports[report_type] = result

        report_results[binary_name] = binary_reports

    # Summary
    print(f"\n{'='*80}")
    print("📊 API REPORT GENERATION SUMMARY")
    print(f"{'='*80}")

    total_reports = len(successful_analyses) * len(report_types)
    successful_reports = sum(
        1
        for binary_reports in report_results.values()
        for report in binary_reports.values()
        if report["success"]
    )

    print(f"📈 Total Reports Attempted: {total_reports}")
    print(f"✅ Successful Reports: {successful_reports}")
    print(f"❌ Failed Reports: {total_reports - successful_reports}")
    print(f"📊 Success Rate: {successful_reports/total_reports*100:.1f}%")

    print("\n📋 DETAILED RESULTS BY BINARY:")
    for binary_name, binary_reports in report_results.items():
        successful_count = sum(1 for r in binary_reports.values() if r["success"])
        total_count = len(binary_reports)
        print(
            f"   📦 {binary_name}: {successful_count}/{total_count} reports successful"
        )

        for report_type, result in binary_reports.items():
            status = "✅" if result["success"] else "❌"
            status_code = result.get("status_code", "N/A")
            print(f"      {status} {report_type}: HTTP {status_code}")

    # Save results
    results_file = (
        f"api_report_generation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    with open(results_file, "w") as f:
        json.dump(report_results, f, indent=2, default=str)

    print(f"\n📄 Results saved to: {results_file}")
    print("🎉 API report generation complete!")

    return report_results


if __name__ == "__main__":
    main()
