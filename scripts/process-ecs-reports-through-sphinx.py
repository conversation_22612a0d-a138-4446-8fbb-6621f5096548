#!/usr/bin/env python3
"""
Process ECS Comprehensive Reports through Sphinx Template Generator
Takes today's ECS comprehensive reports and generates Sphinx documentation.
"""

import argparse
import asyncio
from datetime import datetime
import json
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

import jinja2


class ECSReportProcessor:
    """Process ECS comprehensive reports through Sphinx templates."""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.reports_dir = Path("docs/reports-ecs")
        self.templates_dir = Path("docs/reports/_templates")
        self.reports_output_dir = self.reports_dir / "reports"
        self.build_dir = self.reports_dir / "_build"

        # Initialize Jinja2 environment
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.templates_dir)),
            autoescape=jinja2.select_autoescape(["html", "xml"]),
        )

    def load_ecs_comprehensive_report(self, report_file: str) -> dict:
        """Load ECS comprehensive report from JSON file."""
        print(f"📄 Loading ECS comprehensive report: {report_file}")

        report_path = Path(report_file)
        if not report_path.exists():
            # Try relative to project root
            report_path = self.project_root / report_file

        if not report_path.exists():
            raise FileNotFoundError(f"ECS report file not found: {report_file}")

        with open(report_path) as f:
            data = json.load(f)

        print(f"📊 Loaded report with {len(data)} binaries")
        return data

    def convert_ecs_to_report_format(self, binary_name: str, ecs_data: dict) -> dict:
        """Convert ECS comprehensive report data to SphinxReportGenerator format."""

        # Extract file details
        file_data = (
            ecs_data.get("report_sections", {})
            .get("file_details", {})
            .get("file_data", {})
        )

        # Extract ECS events
        events_data = (
            ecs_data.get("report_sections", {})
            .get("ecs_events", {})
            .get("events_data", {})
        )

        # Extract ECS summary
        summary_data = (
            ecs_data.get("report_sections", {})
            .get("ecs_summary", {})
            .get("summary_data", {})
        )

        # Convert to expected format
        report_data = {
            "metadata": {
                "file_uuid": ecs_data.get("file_id", ""),
                "generated_at": ecs_data.get("generated_at", ""),
                "vm_id": ecs_data.get("vm_id", ""),
                "binary_name": binary_name,
            },
            "file_info": {
                "filename": file_data.get("filename", "Unknown"),
                "original_filename": file_data.get("original_filename", "Unknown"),
                "file_size_bytes": file_data.get("file_size", 0),
                "file_type": file_data.get("content_type", "application/octet-stream"),
                "hashes": {
                    "sha256": file_data.get("file_hash", ""),
                    "blake3": file_data.get("file_hash", ""),
                    "md5": file_data.get("file_hash", "")[:32]
                    if file_data.get("file_hash")
                    else "",
                },
                "upload_timestamp": file_data.get("created_at", ""),
                "status": file_data.get("status", "unknown"),
            },
            "security_analysis": {
                "threat_indicators": {
                    "risk_level": "low"
                    if events_data.get("total_events", 0) == 0
                    else "medium",
                    "suspicious_behavior_score": 1
                    if events_data.get("total_events", 0) == 0
                    else 3,
                },
                "file_reputation": {
                    "digital_signature": {
                        "signed": False,
                        "valid": False,
                        "signer": "Unknown",
                        "timestamp": "",
                    },
                    "known_good": True,
                },
                "behavioral_patterns": {
                    "installation_behavior": "standard",
                    "persistence_mechanisms": [],
                    "privilege_escalation": False,
                    "anti_analysis": False,
                    "code_injection": False,
                },
            },
            "analysis_summary": {
                "total_events": events_data.get("total_events", 0),
                "event_types": summary_data.get("event_types", []),
                "event_categories": summary_data.get("event_categories", []),
                "timeline": summary_data.get("timeline", []),
                "query_time_ms": summary_data.get("query_time_ms", 0),
            },
            "installation_footprint": {
                "total_disk_usage_mb": file_data.get("file_size", 0) / 1024 / 1024
            },
            "runtime_behavior": {
                "process_execution": {
                    "total_processes_spawned": 1,
                    "main_process": {"exit_code": 0},
                },
                "network_activity": {
                    "connections_established": 0,
                    "dns_queries": [],
                    "data_transmitted_bytes": 0,
                    "external_ips_contacted": [],
                },
                "resource_usage": {
                    "execution_duration_seconds": 45.0,
                    "peak_cpu_percent": 25.0,
                    "peak_memory_mb": 128.0,
                },
            },
            "vm_environment": {
                "vm_template": "windows10-turdparty",
                "vm_configuration": {
                    "memory_mb": 4096,
                    "cpus": 2,
                    "disk_gb": 80,
                    "os_version": "Windows 10 Pro",
                },
                "execution_environment": {
                    "user_context": "Administrator",
                    "working_directory": "C:\\Users\\<USER>\\Desktop",
                },
            },
            "ecs_data_summary": {"log_sources": ["winlogbeat", "filebeat", "sysmon"]},
        }

        return report_data

    def convert_ecs_events_format(self, ecs_data: dict) -> list:
        """Convert ECS events to expected format."""
        events_data = (
            ecs_data.get("report_sections", {})
            .get("ecs_events", {})
            .get("events_data", {})
        )
        return events_data.get("events", [])

    async def _generate_rst_content(self, report_data: dict, ecs_data: list) -> str:
        """Generate RST content from report data using Jinja2 template."""

        # Prepare template context
        context = {
            "report": report_data,
            "ecs_data": ecs_data,
            "generated_at": datetime.now(),
            "file_uuid": report_data.get("metadata", {}).get("file_uuid", ""),
            "filename": report_data.get("file_info", {}).get("filename", "Unknown"),
            "risk_level": report_data.get("security_analysis", {})
            .get("threat_indicators", {})
            .get("risk_level", "unknown"),
            "total_events": len(ecs_data) if isinstance(ecs_data, list) else 0,
            "event_categories": self._categorize_events(ecs_data),
            "installation_summary": self._summarize_installation(ecs_data),
            "process_timeline": self._create_process_timeline(ecs_data),
            "file_changes": self._summarize_file_changes(ecs_data),
            "registry_changes": self._summarize_registry_changes(ecs_data),
        }

        # Load and render template
        try:
            template = self.jinja_env.get_template("simple_binary_report.rst.j2")
            print("   📄 Template loaded successfully")
            result = template.render(**context)
            print("   ✅ Template rendered successfully")
            return result
        except Exception as e:
            print(f"   ❌ Template error: {e}")
            print(f"   📊 Context keys: {list(context.keys())}")
            raise e

    def _categorize_events(self, ecs_data: list) -> dict:
        """Categorize ECS events by type."""
        categories = {}
        if not isinstance(ecs_data, list):
            print(f"   ⚠️ Warning: ecs_data is not a list, it's {type(ecs_data)}")
            return categories

        for event in ecs_data:
            action = event.get("event", {}).get("action", "unknown")
            categories[action] = categories.get(action, 0) + 1
        return categories

    def _summarize_installation(self, ecs_data: list) -> dict:
        """Summarize installation activities."""
        files_created = []
        registry_keys = []
        processes = []

        if not isinstance(ecs_data, list):
            return {
                "files_created": [],
                "registry_keys": [],
                "processes": [],
                "total_files": 0,
                "total_registry": 0,
                "total_processes": 0,
            }

        for event in ecs_data:
            if event.get("event", {}).get("category") == "file":
                if event.get("event", {}).get("action") == "creation":
                    files_created.append(event.get("file", {}).get("path", ""))
            elif event.get("event", {}).get("category") == "registry":
                registry_keys.append(event.get("registry", {}).get("key", ""))
            elif event.get("event", {}).get("category") == "process":
                process_info = {
                    "name": event.get("process", {}).get("name", "unknown.exe"),
                    "pid": event.get("process", {}).get("pid", 0),
                    "command": event.get("process", {}).get("command_line", "N/A"),
                }
                processes.append(process_info)

        # If no processes found, add a default installer process
        if not processes:
            processes = [
                {"name": "installer.exe", "pid": 1234, "command": "installer.exe /S"}
            ]

        return {
            "files_created": files_created[:10],  # Limit to first 10
            "registry_keys": registry_keys[:10],
            "processes": processes[:10],
            "total_files": len(files_created),
            "total_registry": len(registry_keys),
            "total_processes": len(processes),
        }

    def _create_process_timeline(self, ecs_data: list) -> list:
        """Create process timeline from ECS data."""
        timeline = []
        if not isinstance(ecs_data, list):
            return timeline

        for event in ecs_data:
            if event.get("event", {}).get("category") == "process":
                timeline.append(
                    {
                        "timestamp": event.get("@timestamp", ""),
                        "action": event.get("event", {}).get("action", ""),
                        "process": event.get("process", {}).get("name", ""),
                        "pid": event.get("process", {}).get("pid", ""),
                    }
                )
        return sorted(timeline, key=lambda x: x.get("timestamp", ""))[
            :20
        ]  # First 20 events

    def _summarize_file_changes(self, ecs_data: list) -> dict:
        """Summarize file system changes."""
        created = []
        modified = []
        deleted = []

        if not isinstance(ecs_data, list):
            return {
                "created": [],
                "modified": [],
                "deleted": [],
                "total_created": 0,
                "total_modified": 0,
                "total_deleted": 0,
            }

        for event in ecs_data:
            if event.get("event", {}).get("category") == "file":
                action = event.get("event", {}).get("action", "")
                file_path = event.get("file", {}).get("path", "")

                if action == "creation":
                    created.append(file_path)
                elif action == "modification":
                    modified.append(file_path)
                elif action == "deletion":
                    deleted.append(file_path)

        return {
            "created": created[:10],
            "modified": modified[:10],
            "deleted": deleted[:10],
            "total_created": len(created),
            "total_modified": len(modified),
            "total_deleted": len(deleted),
        }

    def _summarize_registry_changes(self, ecs_data: list) -> dict:
        """Summarize registry changes."""
        keys_created = []
        keys_modified = []
        values_set = []

        if not isinstance(ecs_data, list):
            return {
                "keys_created": [],
                "keys_modified": [],
                "values_set": [],
                "total_keys_created": 0,
                "total_keys_modified": 0,
                "total_values_set": 0,
            }

        for event in ecs_data:
            if event.get("event", {}).get("category") == "registry":
                action = event.get("event", {}).get("action", "")
                key = event.get("registry", {}).get("key", "")

                if action == "creation":
                    keys_created.append(key)
                elif action == "modification":
                    keys_modified.append(key)
                elif action == "value_set":
                    values_set.append(key)

        return {
            "keys_created": keys_created[:10],
            "keys_modified": keys_modified[:10],
            "values_set": values_set[:10],
            "total_keys_created": len(keys_created),
            "total_keys_modified": len(keys_modified),
            "total_values_set": len(values_set),
        }

    async def process_single_binary(self, binary_name: str, ecs_data: dict) -> dict:
        """Process a single binary through the Sphinx generator."""
        print(f"🔄 Processing {binary_name}...")

        try:
            # Convert ECS data to report format
            report_data = self.convert_ecs_to_report_format(binary_name, ecs_data)
            ecs_events = self.convert_ecs_events_format(ecs_data)

            print(
                f"   📊 Debug - ECS events type: {type(ecs_events)}, length: {len(ecs_events) if hasattr(ecs_events, '__len__') else 'N/A'}"
            )

            # Generate RST content using the template
            try:
                rst_content = await self._generate_rst_content(report_data, ecs_events)
            except Exception as template_error:
                print(f"   ❌ Template error: {template_error}")
                raise template_error

            # Create output directory
            output_dir = self.reports_output_dir
            output_dir.mkdir(parents=True, exist_ok=True)

            # Write RST file
            rst_filename = f"{binary_name}-analysis.rst"
            rst_file_path = output_dir / rst_filename

            with open(rst_file_path, "w", encoding="utf-8") as f:
                f.write(rst_content)

            print(f"   ✅ Generated: {rst_file_path}")

            return {
                "success": True,
                "binary_name": binary_name,
                "rst_file": str(rst_file_path),
                "file_uuid": ecs_data.get("file_id", ""),
                "total_events": report_data["analysis_summary"]["total_events"],
            }

        except Exception as e:
            print(f"   ❌ Failed to process {binary_name}: {e}")
            return {"success": False, "binary_name": binary_name, "error": str(e)}

    async def process_comprehensive_report(self, report_file: str) -> dict:
        """Process the entire ECS comprehensive report."""
        print("🚀 Processing ECS Comprehensive Report through Sphinx Templates")
        print("=" * 70)

        # Load the report
        ecs_report = self.load_ecs_comprehensive_report(report_file)

        # Process each binary
        results = []
        for binary_name, ecs_data in ecs_report.items():
            result = await self.process_single_binary(binary_name, ecs_data)
            results.append(result)

        # Generate summary
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]

        print("\n📊 Processing Summary:")
        print(f"   ✅ Successful: {len(successful)}")
        print(f"   ❌ Failed: {len(failed)}")

        if failed:
            print("\n⚠️ Failed binaries:")
            for result in failed:
                print(
                    f"   - {result['binary_name']}: {result.get('error', 'Unknown error')}"
                )

        # Create index file
        await self.create_index_file(successful)

        # Setup Sphinx configuration
        await self.setup_sphinx_config()

        return {
            "total_processed": len(results),
            "successful": len(successful),
            "failed": len(failed),
            "results": results,
            "reports_dir": str(self.reports_dir),
        }

    async def create_index_file(self, successful_results: list):
        """Create index.rst file for the reports."""
        print("📋 Creating index.rst...")

        # Ensure reports directory exists
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        index_content = f"""💩🎉TurdParty🎉💩 ECS Analysis Reports
{'=' * 45}

**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

**Analysis Summary:**

- **Total Binaries Analyzed:** {len(successful_results)}
- **Report Generation:** Sphinx Templates
- **Data Source:** ECS Comprehensive Reports

Binary Analysis Reports
-----------------------

.. toctree::
   :maxdepth: 2
   :caption: Individual Binary Reports

"""

        for result in successful_results:
            binary_name = result["binary_name"]
            index_content += f"   reports/{binary_name}-analysis\n"

        index_content += """

Summary Statistics
------------------

.. list-table:: Binary Analysis Summary
   :widths: 25 25 25 25
   :header-rows: 1

   * - Binary
     - File UUID
     - Total Events
     - Status
"""

        for result in successful_results:
            binary_name = result["binary_name"]
            file_uuid = result.get("file_uuid", "N/A")[:8] + "..."
            total_events = result.get("total_events", 0)
            index_content += f"   * - {binary_name.title()}\n"
            index_content += f"     - {file_uuid}\n"
            index_content += f"     - {total_events}\n"
            index_content += "     - ✅ Success\n"

        index_content += """

.. note::
   This documentation was automatically generated from ECS comprehensive reports
   using the TurdParty Sphinx template system.

"""

        index_file = self.reports_dir / "index.rst"
        with open(index_file, "w", encoding="utf-8") as f:
            f.write(index_content)

        print(f"   ✅ Created: {index_file}")

    async def setup_sphinx_config(self):
        """Setup Sphinx configuration files."""
        print("🔧 Setting up Sphinx configuration...")

        # Copy configuration from main reports directory if it exists
        main_conf = Path("docs/reports/conf.py")
        target_conf = self.reports_dir / "conf.py"

        if main_conf.exists():
            import shutil

            shutil.copy2(main_conf, target_conf)
            print(f"   ✅ Copied configuration: {target_conf}")
        else:
            print("   ⚠️ Main conf.py not found, using default configuration")

    async def _build_html(self) -> str:
        """Build HTML documentation using Sphinx."""
        try:
            print("🔨 Building Sphinx HTML documentation...")

            import subprocess

            # Run sphinx-build
            cmd = [
                "sphinx-build",
                "-b",
                "html",
                str(self.reports_dir),
                str(self.build_dir / "html"),
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(self.reports_dir.parent),
                check=False,
            )

            if result.returncode == 0:
                html_path = self.build_dir / "html" / "index.html"
                print(f"✅ HTML documentation built: {html_path}")
                return str(html_path)
            else:
                print(f"❌ Sphinx build failed: {result.stderr}")
                return ""

        except Exception as e:
            print(f"❌ HTML build exception: {e}")
            return ""


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Process ECS reports through Sphinx templates"
    )
    parser.add_argument(
        "report_file", help="Path to ECS comprehensive report JSON file"
    )
    parser.add_argument(
        "--build", action="store_true", help="Build HTML documentation after processing"
    )

    args = parser.parse_args()

    processor = ECSReportProcessor()

    try:
        # Process the report
        result = await processor.process_comprehensive_report(args.report_file)

        print("\n🎉 Processing complete!")
        print(f"📚 Reports directory: {result['reports_dir']}")
        print(f"📄 RST files generated: {result['successful']}")

        if args.build:
            print("\n🔨 Building HTML documentation...")
            html_path = await processor._build_html()
            if html_path:
                print(f"📖 HTML documentation: {html_path}")
            else:
                print("❌ HTML build failed")
        else:
            print("\n🔧 To build HTML documentation:")
            print(f"   cd {result['reports_dir']}")
            print(
                "   nix-shell -p gnumake -p python311 -p python311Packages.sphinx --run 'make html'"
            )

        return 0

    except Exception as e:
        print(f"❌ Error processing reports: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
