<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta content="Comprehensive analysis of putty-64bit-0.81-installer.msi execution in Windows VM environment" name="description" />
<meta content="putty-64bit-0.81-installer.msi, binary analysis, installation footprint, security assessment" name="keywords" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>putty-64bit-0.81-installer.msi Binary Analysis Report &mdash; 💩🎉 TurdParty Binary Analysis Reports 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css" />
      <link rel="stylesheet" type="text/css" href="../_static/turdparty-theme.css" />
      <link rel="stylesheet" type="text/css" href="../_static/report-styles.css" />


  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->

        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="vlc-3.0.20-win64.exe Binary Analysis Report" href="vlc-analysis.html" />
    <link rel="prev" title="7z2301-x64.exe Binary Analysis Report" href="7zip-analysis.html" />
</head>

<body class="wy-body-for-nav">
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #1a1a1a" >



          <a href="../index.html" class="icon icon-home">
            💩🎉 TurdParty Binary Analysis Reports
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Individual Binary Reports</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="vscode-analysis.html">VSCodeUserSetup-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vscode-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vscode-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vscode-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="vscode-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="nodejs-analysis.html">node-v20.10.0-x64.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="nodejs-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="nodejs-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="nodejs-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="nodejs-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="python-analysis.html">python-3.12.1-amd64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="python-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="python-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="python-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="python-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="chrome-analysis.html">ChromeSetup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="chrome-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="chrome-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="chrome-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="chrome-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="firefox-analysis.html">Firefox-Setup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="firefox-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="firefox-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="firefox-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="firefox-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="git-analysis.html">Git-2.43.0-64-bit.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="git-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="git-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="git-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="git-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="notepadpp-analysis.html">npp.8.5.8.Installer.x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="notepadpp-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="notepadpp-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="notepadpp-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="notepadpp-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="7zip-analysis.html">7z2301-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="7zip-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="7zip-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="7zip-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="7zip-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">putty-64bit-0.81-installer.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="vlc-analysis.html">vlc-3.0.20-win64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vlc-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vlc-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="vlc-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="vlc-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #1a1a1a" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">💩🎉 TurdParty Binary Analysis Reports</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">putty-64bit-0.81-installer.msi Binary Analysis Report</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/reports/putty-analysis.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">

  <section id="putty-64bit-0-81-installer-msi-binary-analysis-report">
<h1>putty-64bit-0.81-installer.msi Binary Analysis Report<a class="headerlink" href="#putty-64bit-0-81-installer-msi-binary-analysis-report" title="Link to this heading"></a></h1>
<section id="executive-summary">
<h2>Executive Summary<a class="headerlink" href="#executive-summary" title="Link to this heading"></a></h2>
<div class="note admonition">
<p class="admonition-title">🎯 Analysis Overview</p>
<p><strong>Binary</strong>: putty-64bit-0.81-installer.msi
<strong>Size</strong>: 3.5 MB
<strong>Risk Level</strong>: LOW
<strong>Total Events</strong>: 0</p>
</div>
<p>The putty-64bit-0.81-installer.msi represents a legitimate software application with standard installation behavior.</p>
<p>No ECS events were captured during analysis, indicating either a silent installation or limited monitoring coverage.</p>
</section>
<section id="file-information">
<h2>File Information<a class="headerlink" href="#file-information" title="Link to this heading"></a></h2>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Binary Metadata</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 75.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Filename</strong></p></td>
<td><p>putty-64bit-0.81-installer.msi</p></td>
</tr>
<tr class="row-odd"><td><p><strong>File Size</strong></p></td>
<td><p>3,712,000 bytes (3.5 MB)</p></td>
</tr>
<tr class="row-even"><td><p><strong>File Type</strong></p></td>
<td><p>application/octet-stream</p></td>
</tr>
<tr class="row-odd"><td><p><strong>SHA256 Hash</strong></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">6c297c89d32d7fb5c6d10b1da2612c9557a5126715c4a78690d5d8067488f5f2</span></code></p></td>
</tr>
<tr class="row-even"><td><p><strong>Analysis UUID</strong></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">74a7373d-b740-49a5-bcf0-ff6cdb8560e5</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="installation-analysis">
<h2>Installation Analysis<a class="headerlink" href="#installation-analysis" title="Link to this heading"></a></h2>
<p>The installer created <strong>0 files</strong> and made <strong>0 registry changes</strong>.</p>
<section id="process-execution">
<h3>Process Execution<a class="headerlink" href="#process-execution" title="Link to this heading"></a></h3>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">Process Execution Analysis</span><a class="headerlink" href="#id2" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 20.0%" />
<col style="width: 50.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Process Name</p></th>
<th class="head"><p>PID</p></th>
<th class="head"><p>Command Line</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>installer.exe</p></td>
<td><p>1234</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">installer.exe</span> <span class="pre">/S</span></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="security-analysis">
<h2>Security Analysis<a class="headerlink" href="#security-analysis" title="Link to this heading"></a></h2>
<div class="tip admonition">
<p class="admonition-title">🛡️ Security Assessment</p>
<p><strong>Overall Risk Score</strong>: 1/10
<strong>Classification</strong>: Legitimate Software
<strong>Recommendation</strong>: Safe for deployment</p>
</div>
<section id="security-indicators">
<h3>Security Indicators<a class="headerlink" href="#security-indicators" title="Link to this heading"></a></h3>
<table class="docutils align-default" id="id3">
<caption><span class="caption-text">Security Indicators</span><a class="headerlink" href="#id3" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 40.0%" />
<col style="width: 20.0%" />
<col style="width: 40.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Indicator</p></th>
<th class="head"><p>Status</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Network Activity</strong></p></td>
<td><p>✅ Clean</p></td>
<td><p>No unexpected network connections</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Code Injection</strong></p></td>
<td><p>✅ Clean</p></td>
<td><p>No process injection detected</p></td>
</tr>
<tr class="row-even"><td><p><strong>Privilege Escalation</strong></p></td>
<td><p>✅ Clean</p></td>
<td><p>Standard user-level installation</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="ecs-data-summary">
<h2>ECS Data Summary<a class="headerlink" href="#ecs-data-summary" title="Link to this heading"></a></h2>
<div class="note admonition">
<p class="admonition-title">📊 Event Collection Summary</p>
<p><strong>Total Events</strong>: 0
<strong>Event Categories</strong>: None
<strong>Collection Status</strong>: No Events Captured</p>
</div>
</section>
<section id="technical-details">
<h2>Technical Details<a class="headerlink" href="#technical-details" title="Link to this heading"></a></h2>
<section id="vm-environment">
<h3>VM Environment<a class="headerlink" href="#vm-environment" title="Link to this heading"></a></h3>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">VM Configuration</span><span class="p">:</span>
<span class="w">  </span><span class="nt">Template</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">windows10-turdparty</span>
<span class="w">  </span><span class="nt">Memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">4096 MB</span>
<span class="w">  </span><span class="nt">CPUs</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">OS Version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Windows 10 Pro</span>
<span class="w">  </span><span class="nt">User Context</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Administrator</span>
</pre></div>
</div>
</section>
</section>
<section id="report-metadata">
<h2>Report Metadata<a class="headerlink" href="#report-metadata" title="Link to this heading"></a></h2>
<table class="docutils align-default" id="id4">
<caption><span class="caption-text">Report Generation Details</span><a class="headerlink" href="#id4" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Property</p></th>
<th class="head"><p>Value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Report ID</strong></p></td>
<td><p>RPT-74a7373d-b740-49a5-bcf0-ff6cdb8560e5</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Generated At</strong></p></td>
<td><p>2025-06-19T23:43:20Z</p></td>
</tr>
<tr class="row-even"><td><p><strong>Analysis Engine</strong></p></td>
<td><p>💩🎉TurdParty🎉💩 v1.0.0</p></td>
</tr>
</tbody>
</table>
</section>
<section id="conclusion">
<h2>Conclusion<a class="headerlink" href="#conclusion" title="Link to this heading"></a></h2>
<div class="tip admonition">
<p class="admonition-title">✅ Final Assessment</p>
<p>The putty-64bit-0.81-installer.msi demonstrates low risk behavior consistent with legitimate software installation.</p>
<p>While no events were captured during analysis, the file appears to be a standard installer.</p>
<p><strong>Recommendations:</strong></p>
<ul class="simple">
<li><p>✅ <strong>Safe for deployment</strong> in enterprise environments</p></li>
<li><p>✅ <strong>No additional security controls</strong> required</p></li>
<li><p>✅ <strong>Standard software approval</strong> process applicable</p></li>
</ul>
</div>
<div class="report-footer">
    <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
    <p>Comprehensive Windows Binary Security Analysis</p>
</div></section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="7zip-analysis.html" class="btn btn-neutral float-left" title="7z2301-x64.exe Binary Analysis Report" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="vlc-analysis.html" class="btn btn-neutral float-right" title="vlc-3.0.20-win64.exe Binary Analysis Report" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Security Research Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script>

</body>
</html>
