Search.setIndex({"alltitles": {"7z2301-x64.exe Binary Analysis Report": [[1, null]], "Binary Analysis Reports": [[0, "binary-analysis-reports"]], "Binary Analysis Summary": [[0, "id1"]], "Binary Metadata": [[1, "id1"], [2, "id1"], [3, "id1"], [4, "id1"], [5, "id1"], [6, "id1"], [7, "id1"], [8, "id1"], [9, "id1"], [10, "id1"]], "ChromeSetup.exe Binary Analysis Report": [[2, null]], "Conclusion": [[1, "conclusion"], [2, "conclusion"], [3, "conclusion"], [4, "conclusion"], [5, "conclusion"], [6, "conclusion"], [7, "conclusion"], [8, "conclusion"], [9, "conclusion"], [10, "conclusion"]], "ECS Data Summary": [[1, "ecs-data-summary"], [2, "ecs-data-summary"], [3, "ecs-data-summary"], [4, "ecs-data-summary"], [5, "ecs-data-summary"], [6, "ecs-data-summary"], [7, "ecs-data-summary"], [8, "ecs-data-summary"], [9, "ecs-data-summary"], [10, "ecs-data-summary"]], "Executive Summary": [[1, "executive-summary"], [2, "executive-summary"], [3, "executive-summary"], [4, "executive-summary"], [5, "executive-summary"], [6, "executive-summary"], [7, "executive-summary"], [8, "executive-summary"], [9, "executive-summary"], [10, "executive-summary"]], "File Information": [[1, "file-information"], [2, "file-information"], [3, "file-information"], [4, "file-information"], [5, "file-information"], [6, "file-information"], [7, "file-information"], [8, "file-information"], [9, "file-information"], [10, "file-information"]], "Firefox-Setup.exe Binary Analysis Report": [[3, null]], "Git-2.43.0-64-bit.exe Binary Analysis Report": [[4, null]], "Individual Binary Reports": [[0, null]], "Installation Analysis": [[1, "installation-analysis"], [2, "installation-analysis"], [3, "installation-analysis"], [4, "installation-analysis"], [5, "installation-analysis"], [6, "installation-analysis"], [7, "installation-analysis"], [8, "installation-analysis"], [9, "installation-analysis"], [10, "installation-analysis"]], "Process Execution": [[1, "process-execution"], [2, "process-execution"], [3, "process-execution"], [4, "process-execution"], [5, "process-execution"], [6, "process-execution"], [7, "process-execution"], [8, "process-execution"], [9, "process-execution"], [10, "process-execution"]], "Process Execution Analysis": [[1, "id2"], [2, "id2"], [3, "id2"], [4, "id2"], [5, "id2"], [6, "id2"], [7, "id2"], [8, "id2"], [9, "id2"], [10, "id2"]], "Report Generation Details": [[1, "id4"], [2, "id4"], [3, "id4"], [4, "id4"], [5, "id4"], [6, "id4"], [7, "id4"], [8, "id4"], [9, "id4"], [10, "id4"]], "Report Metadata": [[1, "report-metadata"], [2, "report-metadata"], [3, "report-metadata"], [4, "report-metadata"], [5, "report-metadata"], [6, "report-metadata"], [7, "report-metadata"], [8, "report-metadata"], [9, "report-metadata"], [10, "report-metadata"]], "Security Analysis": [[1, "security-analysis"], [2, "security-analysis"], [3, "security-analysis"], [4, "security-analysis"], [5, "security-analysis"], [6, "security-analysis"], [7, "security-analysis"], [8, "security-analysis"], [9, "security-analysis"], [10, "security-analysis"]], "Security Indicators": [[1, "security-indicators"], [1, "id3"], [2, "security-indicators"], [2, "id3"], [3, "security-indicators"], [3, "id3"], [4, "security-indicators"], [4, "id3"], [5, "security-indicators"], [5, "id3"], [6, "security-indicators"], [6, "id3"], [7, "security-indicators"], [7, "id3"], [8, "security-indicators"], [8, "id3"], [9, "security-indicators"], [9, "id3"], [10, "security-indicators"], [10, "id3"]], "Summary Statistics": [[0, "summary-statistics"]], "Technical Details": [[1, "technical-details"], [2, "technical-details"], [3, "technical-details"], [4, "technical-details"], [5, "technical-details"], [6, "technical-details"], [7, "technical-details"], [8, "technical-details"], [9, "technical-details"], [10, "technical-details"]], "VM Environment": [[1, "vm-environment"], [2, "vm-environment"], [3, "vm-environment"], [4, "vm-environment"], [5, "vm-environment"], [6, "vm-environment"], [7, "vm-environment"], [8, "vm-environment"], [9, "vm-environment"], [10, "vm-environment"]], "VSCodeUserSetup-x64.exe Binary Analysis Report": [[10, null]], "node-v20.10.0-x64.msi Binary Analysis Report": [[5, null]], "npp.8.5.8.Installer.x64.exe Binary Analysis Report": [[6, null]], "putty-64bit-0.81-installer.msi Binary Analysis Report": [[7, null]], "python-3.12.1-amd64.exe Binary Analysis Report": [[8, null]], "vlc-3.0.20-win64.exe Binary Analysis Report": [[9, null]], "\u2705 Final Assessment": [[1, null], [2, null], [3, null], [4, null], [5, null], [6, null], [7, null], [8, null], [9, null], [10, null]], "\ud83c\udfaf Analysis Overview": [[1, null], [2, null], [3, null], [4, null], [5, null], [6, null], [7, null], [8, null], [9, null], [10, null]], "\ud83d\udca9\ud83c\udf89TurdParty\ud83c\udf89\ud83d\udca9 ECS Analysis Reports": [[0, null]], "\ud83d\udcca Event Collection Summary": [[1, null], [2, null], [3, null], [4, null], [5, null], [6, null], [7, null], [8, null], [9, null], [10, null]], "\ud83d\udee1\ufe0f Security Assessment": [[1, null], [2, null], [3, null], [4, null], [5, null], [6, null], [7, null], [8, null], [9, null], [10, null]]}, "docnames": ["index", "reports/7zip-analysis", "reports/chrome-analysis", "reports/firefox-analysis", "reports/git-analysis", "reports/nodejs-analysis", "reports/notepadpp-analysis", "reports/putty-analysis", "reports/python-analysis", "reports/vlc-analysis", "reports/vscode-analysis"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["index.rst", "reports/7zip-analysis.rst", "reports/chrome-analysis.rst", "reports/firefox-analysis.rst", "reports/git-analysis.rst", "reports/nodejs-analysis.rst", "reports/notepadpp-analysis.rst", "reports/putty-analysis.rst", "reports/python-analysis.rst", "reports/vlc-analysis.rst", "reports/vscode-analysis.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "0": [0, 1, 2, 3, 6, 8, 10], "000": 7, "040": 4, "06": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "089d18ab0e99": 5, "0974c6db95e2": 1, "1": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10], "10": [0, 1, 2, 3, 4, 6, 7, 8, 9, 10], "106": 10, "10d6": 2, "111": 10, "12": 0, "1234": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "127": 2, "133": 2, "152": 2, "19": 0, "19t23": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "1bd51aa72f45": 3, "2": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "20": 0, "2025": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "20z": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "23": 0, "2437d83db04fb272af8de65eead1a2fc416b9fac3f6af9ce51a627e32b4fe8f8": 8, "25": [5, 8], "26": [5, 8], "26cb6e9f56333682122fafe79dbcdfd51e9f47cc7217dccd29ac6fc33b5598cd": 1, "2a26b9f3": [0, 2], "2d8bde4c": [0, 10], "2e674e94": [0, 9], "2f18": 10, "3": [0, 7], "302": 10, "344": 9, "4": [5, 6, 8, 9], "400e": 2, "4096": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "42": 9, "420": 9, "43": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10], "432": 6, "432f": 4, "437b": 3, "44": 9, "456": 3, "4702": 1, "4769": 9, "47ea": 5, "49a5": 7, "4a78": 8, "4bbb": 3, "4d75": 10, "4f29": 6, "5": [0, 1, 7], "510": 1, "565ef35427041b69921802079f814be9b3edcd054a05cd461706e9857d7909e4": 10, "58": 4, "580": 3, "589": [1, 8], "5a23e4da72dd": 8, "6": 6, "60": 4, "607": 5, "616": 5, "63b0c18f": [0, 8], "64": 0, "64bit": 0, "653579f8": [0, 3], "66810933fca6": 9, "69": 3, "696": 8, "6c297c89d32d7fb5c6d10b1da2612c9557a5126715c4a78690d5d8067488f5f2": 7, "7": 2, "7023282c": [0, 6], "712": 7, "72": 3, "72b3": 8, "74a7373d": [0, 7], "796": 6, "7z2301": 0, "7z2301-x64.ex": 1, "7zip": 0, "8": 0, "81": 0, "8486": 4, "8638fafab83f": 2, "868": 4, "876": 2, "8778": 10, "888": 10, "8b01": 1, "8cad034a7491": 4, "8d1194fac66a38724b5d0952b6687afa522cfdb884aff0f68645252ae6bae6b3": 3, "8e9b": 9, "90ae1666c56988a6cc38424e8b9bffd05defe0e892a46aa606b94f7552cfb581": 5, "9c1c": 5, "At": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "No": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "The": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "a35065f9": [0, 5], "a6058d7c4c16bfa5bcd6fde051a92de8c68535fd7ebade55fc0ab1c41be3c8d5": 4, "aa5325f5a9a1": 6, "activ": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "addit": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "administr": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "af88": 8, "amd64": 0, "analyz": 0, "appear": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "applic": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "approv": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "automat": 0, "b6f8": 6, "b740": 7, "bc5943fe": [0, 1], "bc65": 2, "bc69": 3, "bcf0": 7, "behavior": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "binary analysi": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "bit": 0, "byte": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "c11a": 4, "ca21": 9, "cae3bc4a": [0, 4], "captur": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "categori": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "chang": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "chrome": 0, "chromesetup": 0, "chromesetup.ex": 2, "classif": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "clean": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "code": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "command": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "comprehens": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "conclus": 0, "configur": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "connect": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "consist": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "context": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "control": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "coverag": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "cpu": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "creat": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "d50a46e7ffb799d501d60d9d3689d0b3fbe668d16aa421d67216269f83974220": 6, "d8055b6643651ca5b9ad58c438692a481483657f3f31624cdfa68b92e8394a57": 9, "data": 0, "demonstr": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "deploy": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "descript": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "detail": 0, "detect": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "document": 0, "dure": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "e07b6c68f59c": 10, "e56": 6, "ed20": 1, "either": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "engin": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "enterpris": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "escal": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "event": 0, "ex": [0, 5, 7], "execut": 0, "f46e": 5, "fc827c1bb37c57f81723c9ed9d25311a388d9a8018b176719dcb4a4dc41709b6": 2, "ff6cdb8560e5": 7, "file": 0, "filenam": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "firefox": 0, "firefox-setup.ex": 3, "from": 0, "gener": 0, "git": 0, "git-2.43.0-64-bit.ex": 4, "hash": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "id": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "inform": 0, "inject": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "instal": 0, "installation footprint": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "legitim": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "level": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "limit": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "line": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "low": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "made": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "mb": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "memori": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "metadata": 0, "monitor": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "msi": 0, "name": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "network": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "node": 0, "node-v20.10.0-x64.msi": 5, "nodej": 0, "none": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "notepadpp": 0, "npp": 0, "npp.8.5.8.installer.x64.ex": 6, "o": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "octet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "overal": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "pid": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "platform": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "privileg": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "pro": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "properti": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "putti": 0, "putty-64bit-0.81-installer.msi": 7, "python": 0, "python-3.12.1-amd64.ex": 8, "recommend": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "registri": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "repres": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "requir": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "risk": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "rpt": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "safe": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "score": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "secur": 0, "security assess": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "setup": 0, "sha256": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "silent": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "size": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "softwar": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "sourc": 0, "sphinx": 0, "standard": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "statu": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "stream": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "success": 0, "system": 0, "technic": 0, "templat": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "thi": 0, "total": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "turdparti": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "unexpect": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "us": 0, "user": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "utc": 0, "uuid": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "v1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "v20": 0, "valu": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "version": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "vlc": 0, "vlc-3.0.20-win64.ex": 9, "vscode": 0, "vscodeusersetup": 0, "vscodeusersetup-x64.ex": 10, "wa": 0, "were": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "while": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "win64": 0, "window": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "windows10": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "x64": 0}, "titles": ["\ud83d\udca9\ud83c\udf89TurdParty\ud83c\udf89\ud83d\udca9 ECS Analysis Reports", "7z2301-x64.exe Binary Analysis Report", "ChromeSetup.exe Binary Analysis Report", "Firefox-Setup.exe Binary Analysis Report", "Git-2.43.0-64-bit.exe Binary Analysis Report", "node-v20.10.0-x64.msi Binary Analysis Report", "npp.8.5.8.Installer.x64.exe Binary Analysis Report", "putty-64bit-0.81-installer.msi Binary Analysis Report", "python-3.12.1-amd64.exe Binary Analysis Report", "vlc-3.0.20-win64.exe Binary Analysis Report", "VSCodeUserSetup-x64.exe Binary Analysis Report"], "titleterms": {"0": [4, 5, 7, 9], "1": 8, "10": 5, "12": 8, "2": 4, "20": 9, "3": [8, 9], "43": 4, "5": 6, "64": 4, "64bit": 7, "7z2301": 1, "8": 6, "81": 7, "amd64": 8, "analysi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "assess": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "binari": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "bit": 4, "chromesetup": 2, "collect": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "conclus": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "data": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "detail": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "ec": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "environ": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "event": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "ex": [1, 2, 3, 4, 6, 8, 9, 10], "execut": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "file": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "final": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "firefox": 3, "gener": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "git": 4, "indic": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "individu": 0, "inform": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "instal": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "metadata": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "msi": [5, 7], "node": 5, "npp": 6, "overview": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "process": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "putti": 7, "python": 8, "report": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "secur": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "setup": 3, "statist": 0, "summari": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "technic": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "turdparti": 0, "v20": 5, "vlc": 9, "vm": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "vscodeusersetup": 10, "win64": 9, "x64": [1, 5, 6, 10]}})
