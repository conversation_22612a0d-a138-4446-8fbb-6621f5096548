<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>💩🎉TurdParty🎉💩 ECS Analysis Reports &mdash; 💩🎉 TurdParty Binary Analysis Reports 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css" />
      <link rel="stylesheet" type="text/css" href="_static/turdparty-theme.css" />
      <link rel="stylesheet" type="text/css" href="_static/report-styles.css" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="VSCodeUserSetup-x64.exe Binary Analysis Report" href="reports/vscode-analysis.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #1a1a1a" >

          
          
          <a href="#" class="icon icon-home">
            💩🎉 TurdParty Binary Analysis Reports
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Individual Binary Reports</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="reports/vscode-analysis.html">VSCodeUserSetup-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/nodejs-analysis.html">node-v20.10.0-x64.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/python-analysis.html">python-3.12.1-amd64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/chrome-analysis.html">ChromeSetup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/firefox-analysis.html">Firefox-Setup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/git-analysis.html">Git-2.43.0-64-bit.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/notepadpp-analysis.html">npp.8.5.8.Installer.x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/7zip-analysis.html">7z2301-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/putty-analysis.html">putty-64bit-0.81-installer.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/vlc-analysis.html">vlc-3.0.20-win64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #1a1a1a" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">💩🎉 TurdParty Binary Analysis Reports</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">💩🎉TurdParty🎉💩 ECS Analysis Reports</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="turdparty-ecs-analysis-reports">
<h1>💩🎉TurdParty🎉💩 ECS Analysis Reports<a class="headerlink" href="#turdparty-ecs-analysis-reports" title="Link to this heading"></a></h1>
<p><strong>Generated on:</strong> 2025-06-19 23:43:20 UTC</p>
<p><strong>Analysis Summary:</strong></p>
<ul class="simple">
<li><p><strong>Total Binaries Analyzed:</strong> 10</p></li>
<li><p><strong>Report Generation:</strong> Sphinx Templates</p></li>
<li><p><strong>Data Source:</strong> ECS Comprehensive Reports</p></li>
</ul>
<section id="binary-analysis-reports">
<h2>Binary Analysis Reports<a class="headerlink" href="#binary-analysis-reports" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Individual Binary Reports</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="reports/vscode-analysis.html">VSCodeUserSetup-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/nodejs-analysis.html">node-v20.10.0-x64.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/python-analysis.html">python-3.12.1-amd64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/chrome-analysis.html">ChromeSetup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/firefox-analysis.html">Firefox-Setup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/git-analysis.html">Git-2.43.0-64-bit.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/notepadpp-analysis.html">npp.8.5.8.Installer.x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/7zip-analysis.html">7z2301-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/putty-analysis.html">putty-64bit-0.81-installer.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/vlc-analysis.html">vlc-3.0.20-win64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#installation-analysis">Installation Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#security-analysis">Security Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#technical-details">Technical Details</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="summary-statistics">
<h2>Summary Statistics<a class="headerlink" href="#summary-statistics" title="Link to this heading"></a></h2>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Binary Analysis Summary</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Binary</p></th>
<th class="head"><p>File UUID</p></th>
<th class="head"><p>Total Events</p></th>
<th class="head"><p>Status</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Vscode</p></td>
<td><p>2d8bde4c…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Nodejs</p></td>
<td><p>a35065f9…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Python</p></td>
<td><p>63b0c18f…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Chrome</p></td>
<td><p>2a26b9f3…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Firefox</p></td>
<td><p>653579f8…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Git</p></td>
<td><p>cae3bc4a…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Notepadpp</p></td>
<td><p>7023282c…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>7Zip</p></td>
<td><p>bc5943fe…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-even"><td><p>Putty</p></td>
<td><p>74a7373d…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
<tr class="row-odd"><td><p>Vlc</p></td>
<td><p>2e674e94…</p></td>
<td><p>0</p></td>
<td><p>✅ Success</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation was automatically generated from ECS comprehensive reports
using the TurdParty Sphinx template system.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="reports/vscode-analysis.html" class="btn btn-neutral float-right" title="VSCodeUserSetup-x64.exe Binary Analysis Report" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Security Research Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>