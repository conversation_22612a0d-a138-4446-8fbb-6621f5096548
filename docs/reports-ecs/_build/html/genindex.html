<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; 💩🎉 TurdParty Binary Analysis Reports 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css" />
      <link rel="stylesheet" type="text/css" href="_static/turdparty-theme.css" />
      <link rel="stylesheet" type="text/css" href="_static/report-styles.css" />


  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->

        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/custom.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" />
</head>

<body class="wy-body-for-nav">
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #1a1a1a" >



          <a href="index.html" class="icon icon-home">
            💩🎉 TurdParty Binary Analysis Reports
          </a>
              <div class="version">
                1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Individual Binary Reports</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="reports/vscode-analysis.html">VSCodeUserSetup-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vscode-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vscode-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/nodejs-analysis.html">node-v20.10.0-x64.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/nodejs-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/nodejs-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/python-analysis.html">python-3.12.1-amd64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/python-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/python-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/chrome-analysis.html">ChromeSetup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/chrome-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/chrome-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/firefox-analysis.html">Firefox-Setup.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/firefox-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/firefox-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/git-analysis.html">Git-2.43.0-64-bit.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/git-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/git-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/notepadpp-analysis.html">npp.8.5.8.Installer.x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/notepadpp-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/notepadpp-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/7zip-analysis.html">7z2301-x64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/7zip-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/7zip-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/putty-analysis.html">putty-64bit-0.81-installer.msi Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/putty-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/putty-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reports/vlc-analysis.html">vlc-3.0.20-win64.exe Binary Analysis Report</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#file-information">File Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#installation-analysis">Installation Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#process-execution">Process Execution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#security-analysis">Security Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#security-indicators">Security Indicators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#ecs-data-summary">ECS Data Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#technical-details">Technical Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="reports/vlc-analysis.html#vm-environment">VM Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#report-metadata">Report Metadata</a></li>
<li class="toctree-l2"><a class="reference internal" href="reports/vlc-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #1a1a1a" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">💩🎉 TurdParty Binary Analysis Reports</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">


<h1 id="index">Index</h1>

<div class="genindex-jumpbox">

</div>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, TurdParty Security Research Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script>

</body>
</html>
