#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 VM Scheduler with Packer Integration

This module provides intelligent VM scheduling that integrates with the
Packer manager to ensure required boxes are available before scheduling.

Features:
- Automatic box availability checking
- Background Packer builds when boxes are missing
- Queue management for VM requests
- Integration with existing VM management system
- Telemetry and monitoring
"""

import asyncio
import json
import logging
import time
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Callable

import requests
from elasticsearch import Elasticsearch

from .packer_manager import packer_manager, ensure_box_available, PackerBuildConfig, BuildType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VMRequestStatus(Enum):
    """VM request status enumeration."""
    PENDING = "pending"
    WAITING_FOR_BOX = "waiting_for_box"
    BUILDING_BOX = "building_box"
    READY_TO_SCHEDULE = "ready_to_schedule"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class VMRequest:
    """VM scheduling request."""
    request_id: str
    vm_name: str
    box_name: str
    template_name: str = "windows10"
    memory_mb: int = 4096
    cpus: int = 2
    disk_gb: int = 40
    priority: int = 5  # 1-10, higher is more urgent
    metadata: Dict[str, Any] = None
    created_at: str = None
    status: VMRequestStatus = VMRequestStatus.PENDING
    vm_id: Optional[str] = None
    build_id: Optional[str] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc).isoformat()


@dataclass
class SchedulerTelemetry:
    """Telemetry data for VM scheduler."""
    request_id: str
    vm_id: Optional[str]
    timestamp: str
    event_type: str
    status: VMRequestStatus
    message: str
    duration_seconds: Optional[float] = None
    queue_size: Optional[int] = None
    
    def to_ecs_format(self) -> Dict[str, Any]:
        """Convert to ECS format for Elasticsearch."""
        return {
            "@timestamp": self.timestamp,
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["info"],
                "action": f"vm_scheduler.{self.event_type}",
                "dataset": "turdparty.vm_scheduler",
                "module": "vm_scheduler"
            },
            "turdparty": {
                "request_id": self.request_id,
                "vm_id": self.vm_id,
                "status": self.status.value,
                "duration_seconds": self.duration_seconds,
                "queue_size": self.queue_size
            },
            "message": self.message,
            "labels": {
                "component": "vm_scheduler",
                "service_type": "vm_management"
            }
        }


class VMScheduler:
    """Intelligent VM scheduler with Packer integration."""
    
    def __init__(self,
                 api_base_url: str = "http://localhost:8000",
                 elasticsearch_url: str = "http://elasticsearch.turdparty.localhost:9200",
                 max_concurrent_vms: int = 10,
                 max_concurrent_builds: int = 2):
        self.api_base_url = api_base_url
        self.es = Elasticsearch([elasticsearch_url])
        self.max_concurrent_vms = max_concurrent_vms
        self.max_concurrent_builds = max_concurrent_builds
        
        # Request tracking
        self.pending_requests: List[VMRequest] = []
        self.active_requests: Dict[str, VMRequest] = {}
        self.completed_requests: Dict[str, VMRequest] = {}
        
        # Background worker
        self.worker_running = False
        
    async def start_scheduler(self):
        """Start the VM scheduler background worker."""
        self.worker_running = True
        asyncio.create_task(self._scheduler_worker())
        logger.info("🚀 VM Scheduler started")
        
    async def stop_scheduler(self):
        """Stop the VM scheduler."""
        self.worker_running = False
        logger.info("🛑 VM Scheduler stopped")
        
    async def _scheduler_worker(self):
        """Background worker that processes VM requests."""
        while self.worker_running:
            try:
                # Process pending requests
                await self._process_pending_requests()
                
                # Check active requests
                await self._check_active_requests()
                
                # Clean up completed requests
                self._cleanup_old_requests()
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Scheduler worker error: {e}")
                await asyncio.sleep(30)
                
    async def _process_pending_requests(self):
        """Process pending VM requests."""
        if not self.pending_requests:
            return
            
        # Sort by priority (higher first)
        self.pending_requests.sort(key=lambda r: r.priority, reverse=True)
        
        # Check if we can schedule more VMs
        active_vms = len([r for r in self.active_requests.values() 
                         if r.status in [VMRequestStatus.SCHEDULED, VMRequestStatus.RUNNING]])
        
        if active_vms >= self.max_concurrent_vms:
            return
            
        # Process highest priority request
        request = self.pending_requests[0]
        
        try:
            # Check if box is available
            available_boxes = packer_manager.list_available_boxes()
            box_exists = any(box["name"] == request.box_name for box in available_boxes)
            
            if box_exists:
                # Box available, proceed with VM creation
                await self._schedule_vm(request)
            else:
                # Box not available, trigger build
                await self._ensure_box_available(request)
                
        except Exception as e:
            logger.error(f"❌ Failed to process request {request.request_id}: {e}")
            request.status = VMRequestStatus.FAILED
            self._send_telemetry(SchedulerTelemetry(
                request_id=request.request_id,
                vm_id=request.vm_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                event_type="request_failed",
                status=request.status,
                message=f"Failed to process request: {e}"
            ))
            
            # Move to completed
            self.pending_requests.remove(request)
            self.completed_requests[request.request_id] = request
            
    async def _ensure_box_available(self, request: VMRequest):
        """Ensure required box is available, building if necessary."""
        request.status = VMRequestStatus.WAITING_FOR_BOX
        
        # Send telemetry
        self._send_telemetry(SchedulerTelemetry(
            request_id=request.request_id,
            vm_id=request.vm_id,
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type="waiting_for_box",
            status=request.status,
            message=f"Waiting for box: {request.box_name}"
        ))
        
        # Check if box build is already in progress
        active_builds = packer_manager.active_builds
        box_building = any(
            config.output_box_name == request.box_name 
            for config in [packer_manager.build_queue[i] for i in range(len(packer_manager.build_queue))]
        )
        
        if not box_building:
            # Start box build
            request.status = VMRequestStatus.BUILDING_BOX
            
            # Determine ISO path based on template
            iso_path = self._get_iso_path(request.template_name)
            
            config = PackerBuildConfig(
                build_id=f"vm-req-{request.request_id[:8]}",
                build_type=BuildType.FRESH,
                template_name=request.template_name,
                iso_path=iso_path,
                output_box_name=request.box_name,
                vm_uuid=str(uuid.uuid4()),
                memory_mb=request.memory_mb,
                cpus=request.cpus,
                disk_gb=request.disk_gb,
                metadata={
                    "triggered_by": "vm_scheduler",
                    "request_id": request.request_id
                }
            )
            
            request.build_id = packer_manager.queue_build(config)
            
            # Send telemetry
            self._send_telemetry(SchedulerTelemetry(
                request_id=request.request_id,
                vm_id=request.vm_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                event_type="box_build_started",
                status=request.status,
                message=f"Started building box: {request.box_name}"
            ))
            
        # Move to active requests
        self.pending_requests.remove(request)
        self.active_requests[request.request_id] = request
        
    def _get_iso_path(self, template_name: str) -> str:
        """Get ISO path for template."""
        iso_mapping = {
            "windows10": "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab/isos/Win10_22H2_English_x64v1.iso",
            "windows11": "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab/isos/Win11_22H2_English_x64v1.iso",
            # Add more mappings as needed
        }
        
        return iso_mapping.get(template_name, iso_mapping["windows10"])
        
    async def _schedule_vm(self, request: VMRequest):
        """Schedule VM creation via API."""
        try:
            vm_config = {
                "name": request.vm_name,
                "template": request.box_name,
                "vm_type": "vagrant",
                "provider": "virtualbox",
                "memory_mb": request.memory_mb,
                "cpus": request.cpus,
                "disk_gb": request.disk_gb,
                "description": f"Scheduled VM for request {request.request_id}",
                "tags": ["scheduled", "vm_scheduler"],
                "metadata": request.metadata
            }
            
            response = requests.post(
                f"{self.api_base_url}/api/v1/vms/",
                json=vm_config,
                timeout=120
            )
            
            if response.status_code in [200, 201]:
                vm_info = response.json()
                request.vm_id = vm_info.get("vm_id")
                request.status = VMRequestStatus.SCHEDULED
                
                # Send telemetry
                self._send_telemetry(SchedulerTelemetry(
                    request_id=request.request_id,
                    vm_id=request.vm_id,
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    event_type="vm_scheduled",
                    status=request.status,
                    message=f"VM scheduled successfully: {request.vm_id}"
                ))
                
                # Move to active requests
                self.pending_requests.remove(request)
                self.active_requests[request.request_id] = request
                
                logger.info(f"✅ VM scheduled: {request.vm_id} for request {request.request_id}")
                
            else:
                raise Exception(f"API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"❌ Failed to schedule VM for request {request.request_id}: {e}")
            request.status = VMRequestStatus.FAILED
            
            # Send telemetry
            self._send_telemetry(SchedulerTelemetry(
                request_id=request.request_id,
                vm_id=request.vm_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                event_type="vm_schedule_failed",
                status=request.status,
                message=f"Failed to schedule VM: {e}"
            ))
            
            # Move to completed
            self.pending_requests.remove(request)
            self.completed_requests[request.request_id] = request
            
    async def _check_active_requests(self):
        """Check status of active requests."""
        for request_id, request in list(self.active_requests.items()):
            try:
                if request.status == VMRequestStatus.BUILDING_BOX:
                    # Check if box build is complete
                    if request.build_id:
                        build_status = packer_manager.get_build_status(request.build_id)
                        if build_status:
                            if build_status["status"] == "completed":
                                # Box build complete, schedule VM
                                request.status = VMRequestStatus.READY_TO_SCHEDULE
                                await self._schedule_vm(request)
                            elif build_status["status"] == "failed":
                                # Box build failed
                                request.status = VMRequestStatus.FAILED
                                self._send_telemetry(SchedulerTelemetry(
                                    request_id=request.request_id,
                                    vm_id=request.vm_id,
                                    timestamp=datetime.now(timezone.utc).isoformat(),
                                    event_type="box_build_failed",
                                    status=request.status,
                                    message="Box build failed"
                                ))
                                
                                # Move to completed
                                del self.active_requests[request_id]
                                self.completed_requests[request_id] = request
                                
                elif request.status in [VMRequestStatus.SCHEDULED, VMRequestStatus.RUNNING]:
                    # Check VM status
                    if request.vm_id:
                        vm_status = await self._get_vm_status(request.vm_id)
                        if vm_status:
                            if vm_status == "running":
                                request.status = VMRequestStatus.RUNNING
                            elif vm_status in ["stopped", "terminated"]:
                                request.status = VMRequestStatus.COMPLETED
                                
                                # Send telemetry
                                self._send_telemetry(SchedulerTelemetry(
                                    request_id=request.request_id,
                                    vm_id=request.vm_id,
                                    timestamp=datetime.now(timezone.utc).isoformat(),
                                    event_type="vm_completed",
                                    status=request.status,
                                    message="VM completed"
                                ))
                                
                                # Move to completed
                                del self.active_requests[request_id]
                                self.completed_requests[request_id] = request
                                
            except Exception as e:
                logger.error(f"❌ Error checking request {request_id}: {e}")
                
    async def _get_vm_status(self, vm_id: str) -> Optional[str]:
        """Get VM status from API."""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/v1/vms/{vm_id}/status",
                timeout=30
            )
            
            if response.status_code == 200:
                status_info = response.json()
                return status_info.get("status")
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to get VM status for {vm_id}: {e}")
            
        return None
        
    def _cleanup_old_requests(self):
        """Clean up old completed requests."""
        # Keep only last 1000 completed requests
        if len(self.completed_requests) > 1000:
            # Sort by creation time and keep newest
            sorted_requests = sorted(
                self.completed_requests.items(),
                key=lambda x: x[1].created_at,
                reverse=True
            )
            
            # Keep only newest 1000
            self.completed_requests = dict(sorted_requests[:1000])
            
    def _send_telemetry(self, telemetry: SchedulerTelemetry):
        """Send telemetry to Elasticsearch."""
        try:
            doc = telemetry.to_ecs_format()
            
            # Use date-based index
            date_str = datetime.now().strftime("%Y.%m.%d")
            index_name = f"turdparty-vm-scheduler-{date_str}"
            
            self.es.index(
                index=index_name,
                body=doc,
                id=f"{telemetry.request_id}-{int(time.time() * 1000)}"
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to send telemetry: {e}")
            
    def request_vm(self, vm_name: str, box_name: str, 
                   template_name: str = "windows10",
                   memory_mb: int = 4096, cpus: int = 2, disk_gb: int = 40,
                   priority: int = 5, metadata: Dict[str, Any] = None) -> str:
        """Request a VM to be scheduled."""
        request_id = f"vm-req-{uuid.uuid4().hex[:8]}"
        
        request = VMRequest(
            request_id=request_id,
            vm_name=vm_name,
            box_name=box_name,
            template_name=template_name,
            memory_mb=memory_mb,
            cpus=cpus,
            disk_gb=disk_gb,
            priority=priority,
            metadata=metadata or {}
        )
        
        self.pending_requests.append(request)
        
        # Send telemetry
        self._send_telemetry(SchedulerTelemetry(
            request_id=request_id,
            vm_id=None,
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type="vm_requested",
            status=request.status,
            message=f"VM requested: {vm_name}",
            queue_size=len(self.pending_requests)
        ))
        
        logger.info(f"📋 VM requested: {request_id} ({vm_name})")
        return request_id
        
    def get_request_status(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a VM request."""
        # Check pending
        for request in self.pending_requests:
            if request.request_id == request_id:
                return asdict(request)
                
        # Check active
        if request_id in self.active_requests:
            return asdict(self.active_requests[request_id])
            
        # Check completed
        if request_id in self.completed_requests:
            return asdict(self.completed_requests[request_id])
            
        return None
        
    def list_requests(self, status_filter: Optional[VMRequestStatus] = None) -> List[Dict[str, Any]]:
        """List VM requests with optional status filter."""
        all_requests = []
        
        # Add pending
        all_requests.extend([asdict(r) for r in self.pending_requests])
        
        # Add active
        all_requests.extend([asdict(r) for r in self.active_requests.values()])
        
        # Add completed (last 100)
        completed_list = list(self.completed_requests.values())[-100:]
        all_requests.extend([asdict(r) for r in completed_list])
        
        # Filter by status if requested
        if status_filter:
            all_requests = [r for r in all_requests if r["status"] == status_filter.value]
            
        return all_requests


# Global instance
vm_scheduler = VMScheduler()


if __name__ == "__main__":
    # Example usage
    async def main():
        await vm_scheduler.start_scheduler()
        
        # Request a VM
        request_id = vm_scheduler.request_vm(
            vm_name="test-vm-001",
            box_name="10Baht/windows10-turdparty",
            priority=8
        )
        
        print(f"Requested VM: {request_id}")
        
        # Keep running
        await asyncio.sleep(60)
        await vm_scheduler.stop_scheduler()
        
    asyncio.run(main())
