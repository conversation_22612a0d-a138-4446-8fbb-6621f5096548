#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Packer API Endpoints

FastAPI endpoints for Packer build management and VM scheduling integration.

Features:
- Packer build management (start, stop, status, list)
- VM scheduling with automatic box provisioning
- Build telemetry and monitoring
- Box cloning and updating
- Integration with existing TurdParty API
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field

from ..packer_manager import packer_manager, PackerBuildConfig, BuildType, BuildStatus
from ..vm_scheduler import vm_scheduler, VMRequest, VMRequestStatus

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/packer", tags=["packer"])


# Pydantic models for API
class PackerBuildRequest(BaseModel):
    """Request model for starting a Packer build."""
    template_name: str = Field(..., description="Packer template name (e.g., 'windows10')")
    iso_path: str = Field(..., description="Path to ISO file")
    output_box_name: str = Field(..., description="Name for output Vagrant box")
    build_type: str = Field(default="fresh", description="Build type: fresh, clone, update")
    base_box_id: Optional[str] = Field(None, description="Base box for clones/updates")
    vm_uuid: Optional[str] = Field(None, description="VM UUID for tracking")
    memory_mb: int = Field(default=4096, description="Memory allocation in MB")
    cpus: int = Field(default=2, description="CPU count")
    disk_gb: int = Field(default=40, description="Disk size in GB")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class PackerBuildResponse(BaseModel):
    """Response model for Packer build operations."""
    build_id: str
    status: str
    message: str
    vm_uuid: Optional[str] = None
    created_at: str


class PackerBuildStatus(BaseModel):
    """Model for Packer build status."""
    build_id: str
    status: str
    active: bool
    vm_uuid: Optional[str] = None
    template_name: Optional[str] = None
    output_box_name: Optional[str] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None


class VMScheduleRequest(BaseModel):
    """Request model for VM scheduling."""
    vm_name: str = Field(..., description="Name for the VM")
    box_name: str = Field(..., description="Vagrant box name to use")
    template_name: str = Field(default="windows10", description="Template for box building")
    memory_mb: int = Field(default=4096, description="Memory allocation in MB")
    cpus: int = Field(default=2, description="CPU count")
    disk_gb: int = Field(default=40, description="Disk size in GB")
    priority: int = Field(default=5, description="Priority (1-10, higher is more urgent)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class VMScheduleResponse(BaseModel):
    """Response model for VM scheduling."""
    request_id: str
    status: str
    message: str
    vm_name: str
    box_name: str
    created_at: str


class BoxInfo(BaseModel):
    """Model for Vagrant box information."""
    name: str
    provider: str
    version: str


# Packer Build Endpoints
@router.post("/builds", response_model=PackerBuildResponse)
async def start_packer_build(
    request: PackerBuildRequest,
    background_tasks: BackgroundTasks
) -> PackerBuildResponse:
    """Start a new Packer build."""
    try:
        # Validate build type
        try:
            build_type = BuildType(request.build_type.lower())
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid build type: {request.build_type}. Must be one of: fresh, clone, update"
            )
        
        # Create build configuration
        config = PackerBuildConfig(
            build_id=f"api-{datetime.now().strftime('%Y%m%d-%H%M%S')}-{request.template_name}",
            build_type=build_type,
            template_name=request.template_name,
            iso_path=request.iso_path,
            output_box_name=request.output_box_name,
            base_box_id=request.base_box_id,
            vm_uuid=request.vm_uuid,
            memory_mb=request.memory_mb,
            cpus=request.cpus,
            disk_gb=request.disk_gb,
            metadata=request.metadata
        )
        
        # Queue the build
        build_id = packer_manager.queue_build(config)
        
        logger.info(f"🚀 Packer build queued via API: {build_id}")
        
        return PackerBuildResponse(
            build_id=build_id,
            status="queued",
            message=f"Packer build queued successfully: {request.template_name}",
            vm_uuid=config.vm_uuid,
            created_at=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to start Packer build: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start build: {str(e)}")


@router.get("/builds", response_model=List[PackerBuildStatus])
async def list_packer_builds() -> List[PackerBuildStatus]:
    """List all Packer builds."""
    try:
        builds = []
        
        # Add active builds
        for build_id, process in packer_manager.active_builds.items():
            builds.append(PackerBuildStatus(
                build_id=build_id,
                status="building",
                active=True,
                vm_uuid=None,  # Would need to track this
                template_name=None,  # Would need to track this
                output_box_name=None,  # Would need to track this
                created_at=None  # Would need to track this
            ))
        
        # Add queued builds
        for i, config in enumerate(packer_manager.build_queue):
            builds.append(PackerBuildStatus(
                build_id=config.build_id,
                status="queued",
                active=False,
                vm_uuid=config.vm_uuid,
                template_name=config.template_name,
                output_box_name=config.output_box_name,
                created_at=None  # Would need to track this
            ))
        
        # Add recent completed builds from history
        for build_id, telemetry in list(packer_manager.build_history.items())[-20:]:
            builds.append(PackerBuildStatus(
                build_id=build_id,
                status=telemetry.status.value,
                active=False,
                vm_uuid=telemetry.vm_uuid,
                template_name=None,  # Would need to track this
                output_box_name=None,  # Would need to track this
                created_at=telemetry.timestamp,
                error_message=telemetry.message if telemetry.status == BuildStatus.FAILED else None
            ))
        
        return builds
        
    except Exception as e:
        logger.error(f"❌ Failed to list builds: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list builds: {str(e)}")


@router.get("/builds/{build_id}", response_model=PackerBuildStatus)
async def get_packer_build_status(build_id: str) -> PackerBuildStatus:
    """Get status of a specific Packer build."""
    try:
        status_info = packer_manager.get_build_status(build_id)
        
        if not status_info:
            raise HTTPException(status_code=404, detail=f"Build not found: {build_id}")
        
        return PackerBuildStatus(
            build_id=build_id,
            status=status_info["status"],
            active=status_info["active"],
            vm_uuid=None,  # Would need to track this
            template_name=None,  # Would need to track this
            output_box_name=None  # Would need to track this
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get build status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get build status: {str(e)}")


@router.delete("/builds/{build_id}")
async def cancel_packer_build(build_id: str) -> Dict[str, str]:
    """Cancel a Packer build."""
    try:
        # Check if build is active
        if build_id in packer_manager.active_builds:
            process = packer_manager.active_builds[build_id]
            process.terminate()
            del packer_manager.active_builds[build_id]
            
            logger.info(f"🛑 Cancelled active build: {build_id}")
            return {"message": f"Build cancelled: {build_id}", "status": "cancelled"}
        
        # Check if build is queued
        for i, config in enumerate(packer_manager.build_queue):
            if config.build_id == build_id:
                del packer_manager.build_queue[i]
                logger.info(f"🛑 Removed queued build: {build_id}")
                return {"message": f"Queued build removed: {build_id}", "status": "cancelled"}
        
        raise HTTPException(status_code=404, detail=f"Build not found or not cancellable: {build_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to cancel build: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel build: {str(e)}")


# Box Management Endpoints
@router.get("/boxes", response_model=List[BoxInfo])
async def list_vagrant_boxes() -> List[BoxInfo]:
    """List all available Vagrant boxes."""
    try:
        boxes = packer_manager.list_available_boxes()
        return [BoxInfo(**box) for box in boxes]
        
    except Exception as e:
        logger.error(f"❌ Failed to list boxes: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list boxes: {str(e)}")


@router.post("/boxes/{source_box_name}/clone")
async def clone_vagrant_box(
    source_box_name: str,
    target_box_name: str,
    modifications: Optional[Dict[str, Any]] = None
) -> PackerBuildResponse:
    """Clone an existing Vagrant box with optional modifications."""
    try:
        build_id = packer_manager.clone_box(
            source_box_name=source_box_name,
            target_box_name=target_box_name,
            modifications=modifications
        )
        
        logger.info(f"📋 Box clone queued: {source_box_name} -> {target_box_name}")
        
        return PackerBuildResponse(
            build_id=build_id,
            status="queued",
            message=f"Box clone queued: {source_box_name} -> {target_box_name}",
            created_at=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to clone box: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clone box: {str(e)}")


@router.post("/boxes/{box_name}/update")
async def update_vagrant_box(
    box_name: str,
    update_type: str = "security"
) -> PackerBuildResponse:
    """Update an existing Vagrant box with latest patches."""
    try:
        build_id = packer_manager.update_box(
            box_name=box_name,
            update_type=update_type
        )
        
        logger.info(f"📋 Box update queued: {box_name} ({update_type})")
        
        return PackerBuildResponse(
            build_id=build_id,
            status="queued",
            message=f"Box update queued: {box_name} ({update_type})",
            created_at=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to update box: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update box: {str(e)}")


# VM Scheduling Endpoints
@router.post("/schedule", response_model=VMScheduleResponse)
async def schedule_vm(request: VMScheduleRequest) -> VMScheduleResponse:
    """Schedule a VM with automatic box provisioning if needed."""
    try:
        request_id = vm_scheduler.request_vm(
            vm_name=request.vm_name,
            box_name=request.box_name,
            template_name=request.template_name,
            memory_mb=request.memory_mb,
            cpus=request.cpus,
            disk_gb=request.disk_gb,
            priority=request.priority,
            metadata=request.metadata
        )
        
        logger.info(f"📋 VM scheduled via API: {request_id} ({request.vm_name})")
        
        return VMScheduleResponse(
            request_id=request_id,
            status="pending",
            message=f"VM scheduling request created: {request.vm_name}",
            vm_name=request.vm_name,
            box_name=request.box_name,
            created_at=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to schedule VM: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to schedule VM: {str(e)}")


@router.get("/schedule", response_model=List[Dict[str, Any]])
async def list_vm_requests(status_filter: Optional[str] = None) -> List[Dict[str, Any]]:
    """List VM scheduling requests."""
    try:
        # Convert status filter if provided
        status_enum = None
        if status_filter:
            try:
                status_enum = VMRequestStatus(status_filter.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status filter: {status_filter}"
                )
        
        requests = vm_scheduler.list_requests(status_filter=status_enum)
        return requests
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to list VM requests: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list VM requests: {str(e)}")


@router.get("/schedule/{request_id}", response_model=Dict[str, Any])
async def get_vm_request_status(request_id: str) -> Dict[str, Any]:
    """Get status of a VM scheduling request."""
    try:
        request_info = vm_scheduler.get_request_status(request_id)
        
        if not request_info:
            raise HTTPException(status_code=404, detail=f"VM request not found: {request_id}")
        
        return request_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get VM request status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get VM request status: {str(e)}")


# Health and Status Endpoints
@router.get("/health")
async def packer_health() -> Dict[str, Any]:
    """Get Packer manager health status."""
    try:
        return {
            "status": "healthy",
            "active_builds": len(packer_manager.active_builds),
            "queued_builds": len(packer_manager.build_queue),
            "worker_running": packer_manager.worker_running,
            "vm_scheduler_running": vm_scheduler.worker_running,
            "pending_vm_requests": len(vm_scheduler.pending_requests),
            "active_vm_requests": len(vm_scheduler.active_requests),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/stats")
async def packer_stats() -> Dict[str, Any]:
    """Get Packer and VM scheduler statistics."""
    try:
        # Get box statistics
        available_boxes = packer_manager.list_available_boxes()
        
        return {
            "packer": {
                "active_builds": len(packer_manager.active_builds),
                "queued_builds": len(packer_manager.build_queue),
                "total_builds_history": len(packer_manager.build_history),
                "max_concurrent_builds": packer_manager.max_concurrent_builds
            },
            "vm_scheduler": {
                "pending_requests": len(vm_scheduler.pending_requests),
                "active_requests": len(vm_scheduler.active_requests),
                "completed_requests": len(vm_scheduler.completed_requests),
                "max_concurrent_vms": vm_scheduler.max_concurrent_vms
            },
            "boxes": {
                "total_available": len(available_boxes),
                "by_provider": {}
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


# Initialize services on startup
@router.on_event("startup")
async def startup_packer_services():
    """Initialize Packer and VM scheduler services."""
    try:
        # Start Packer manager
        await packer_manager.start_background_worker()
        
        # Start VM scheduler
        await vm_scheduler.start_scheduler()
        
        logger.info("🚀 Packer and VM scheduler services started")
        
    except Exception as e:
        logger.error(f"❌ Failed to start services: {e}")


@router.on_event("shutdown")
async def shutdown_packer_services():
    """Shutdown Packer and VM scheduler services."""
    try:
        # Stop services
        await packer_manager.stop_background_worker()
        await vm_scheduler.stop_scheduler()
        
        logger.info("🛑 Packer and VM scheduler services stopped")
        
    except Exception as e:
        logger.error(f"❌ Failed to stop services: {e}")


# Export router
__all__ = ["router"]
