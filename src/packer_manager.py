#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Packer Build Manager

This module manages Packer builds for Windows VMs with comprehensive
telemetry, cloning, updating, and background build scheduling.

Features:
- Build process monitoring with ECS telemetry
- VM cloning and updating capabilities
- Security metadata extraction from patching
- Background build scheduling and worker flow
- Integration with TurdParty VM scheduling system
"""

import asyncio
import json
import logging
import os
import subprocess
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from enum import Enum

import requests
from elasticsearch import Elasticsearch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BuildStatus(Enum):
    """Packer build status enumeration."""
    PENDING = "pending"
    BUILDING = "building"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BuildType(Enum):
    """Packer build type enumeration."""
    FRESH = "fresh"
    CLONE = "clone"
    UPDATE = "update"


@dataclass
class PackerBuildConfig:
    """Configuration for a Packer build."""
    build_id: str
    build_type: BuildType
    template_name: str
    iso_path: str
    output_box_name: str
    base_box_id: Optional[str] = None  # For clones/updates
    vm_uuid: Optional[str] = None
    memory_mb: int = 4096
    cpus: int = 2
    disk_gb: int = 40
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class BuildTelemetry:
    """Telemetry data for Packer builds."""
    build_id: str
    vm_uuid: str
    timestamp: str
    event_type: str
    phase: str
    status: BuildStatus
    message: str
    duration_seconds: Optional[float] = None
    system_metrics: Optional[Dict[str, Any]] = None
    security_metadata: Optional[Dict[str, Any]] = None
    
    def to_ecs_format(self) -> Dict[str, Any]:
        """Convert to ECS format for Elasticsearch."""
        return {
            "@timestamp": self.timestamp,
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["info"],
                "action": f"packer.{self.event_type}",
                "dataset": "turdparty.packer",
                "module": "packer_manager"
            },
            "turdparty": {
                "build_id": self.build_id,
                "vm_uuid": self.vm_uuid,
                "phase": self.phase,
                "status": self.status.value,
                "duration_seconds": self.duration_seconds,
                "system_metrics": self.system_metrics,
                "security_metadata": self.security_metadata
            },
            "message": self.message,
            "labels": {
                "component": "packer_manager",
                "build_type": "vm_provisioning"
            }
        }


class PackerManager:
    """Manages Packer builds with telemetry and scheduling."""
    
    def __init__(self, 
                 elasticsearch_url: str = "http://elasticsearch.turdparty.localhost:9200",
                 packer_templates_dir: str = "vendor/10b/packer-templates",
                 vagrant_boxes_dir: str = "~/.vagrant.d/boxes",
                 max_concurrent_builds: int = 2):
        self.es = Elasticsearch([elasticsearch_url])
        self.packer_templates_dir = Path(packer_templates_dir)
        self.vagrant_boxes_dir = Path(vagrant_boxes_dir).expanduser()
        self.max_concurrent_builds = max_concurrent_builds
        
        # Build tracking
        self.active_builds: Dict[str, subprocess.Popen] = {}
        self.build_queue: List[PackerBuildConfig] = []
        self.build_history: Dict[str, BuildTelemetry] = {}
        
        # Background worker
        self.worker_executor = ThreadPoolExecutor(max_workers=max_concurrent_builds)
        self.worker_running = False
        
    async def start_background_worker(self):
        """Start the background build worker."""
        self.worker_running = True
        asyncio.create_task(self._background_worker())
        logger.info("🚀 Packer background worker started")
        
    async def stop_background_worker(self):
        """Stop the background build worker."""
        self.worker_running = False
        self.worker_executor.shutdown(wait=True)
        logger.info("🛑 Packer background worker stopped")
        
    async def _background_worker(self):
        """Background worker that processes the build queue."""
        while self.worker_running:
            try:
                # Check for queued builds
                if (len(self.build_queue) > 0 and 
                    len(self.active_builds) < self.max_concurrent_builds):
                    
                    build_config = self.build_queue.pop(0)
                    await self._start_build(build_config)
                    
                # Check for completed builds
                completed_builds = []
                for build_id, process in self.active_builds.items():
                    if process.poll() is not None:
                        completed_builds.append(build_id)
                        
                for build_id in completed_builds:
                    await self._handle_build_completion(build_id)
                    
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Background worker error: {e}")
                await asyncio.sleep(10)
                
    def queue_build(self, config: PackerBuildConfig) -> str:
        """Queue a Packer build for background processing."""
        self.build_queue.append(config)
        
        # Send telemetry
        telemetry = BuildTelemetry(
            build_id=config.build_id,
            vm_uuid=config.vm_uuid or str(uuid.uuid4()),
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type="build_queued",
            phase="queue",
            status=BuildStatus.PENDING,
            message=f"Packer build queued: {config.template_name}"
        )
        self._send_telemetry(telemetry)
        
        logger.info(f"📋 Queued Packer build: {config.build_id}")
        return config.build_id
        
    async def _start_build(self, config: PackerBuildConfig):
        """Start a Packer build process."""
        try:
            # Generate VM UUID if not provided
            if not config.vm_uuid:
                config.vm_uuid = str(uuid.uuid4())
                
            # Prepare build environment
            build_dir = self.packer_templates_dir / "windows" / config.template_name
            if not build_dir.exists():
                raise FileNotFoundError(f"Packer template not found: {build_dir}")
                
            # Build command
            cmd = [
                "bash", "build.sh", 
                config.iso_path,
                "--vm-uuid", config.vm_uuid,
                "--memory", str(config.memory_mb),
                "--cpus", str(config.cpus),
                "--disk", str(config.disk_gb)
            ]
            
            # Start process
            process = subprocess.Popen(
                cmd,
                cwd=build_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.active_builds[config.build_id] = process
            
            # Send start telemetry
            telemetry = BuildTelemetry(
                build_id=config.build_id,
                vm_uuid=config.vm_uuid,
                timestamp=datetime.now(timezone.utc).isoformat(),
                event_type="build_started",
                phase="initialization",
                status=BuildStatus.BUILDING,
                message=f"Packer build started: {config.template_name}"
            )
            self._send_telemetry(telemetry)
            
            # Start output monitoring
            asyncio.create_task(self._monitor_build_output(config, process))
            
            logger.info(f"🚀 Started Packer build: {config.build_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to start build {config.build_id}: {e}")
            
            # Send failure telemetry
            telemetry = BuildTelemetry(
                build_id=config.build_id,
                vm_uuid=config.vm_uuid or "unknown",
                timestamp=datetime.now(timezone.utc).isoformat(),
                event_type="build_failed",
                phase="initialization",
                status=BuildStatus.FAILED,
                message=f"Failed to start build: {e}"
            )
            self._send_telemetry(telemetry)
            
    async def _monitor_build_output(self, config: PackerBuildConfig, process: subprocess.Popen):
        """Monitor Packer build output and extract telemetry."""
        current_phase = "initialization"
        start_time = time.time()
        
        try:
            while process.poll() is None:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    
                    # Parse Packer output for phase detection
                    new_phase = self._detect_build_phase(line)
                    if new_phase != current_phase:
                        # Phase transition
                        duration = time.time() - start_time
                        
                        telemetry = BuildTelemetry(
                            build_id=config.build_id,
                            vm_uuid=config.vm_uuid,
                            timestamp=datetime.now(timezone.utc).isoformat(),
                            event_type="phase_transition",
                            phase=new_phase,
                            status=BuildStatus.BUILDING,
                            message=f"Phase transition: {current_phase} -> {new_phase}",
                            duration_seconds=duration
                        )
                        self._send_telemetry(telemetry)
                        
                        current_phase = new_phase
                        start_time = time.time()
                    
                    # Extract security metadata from Windows Update output
                    security_metadata = self._extract_security_metadata(line)
                    if security_metadata:
                        telemetry = BuildTelemetry(
                            build_id=config.build_id,
                            vm_uuid=config.vm_uuid,
                            timestamp=datetime.now(timezone.utc).isoformat(),
                            event_type="security_update",
                            phase=current_phase,
                            status=BuildStatus.BUILDING,
                            message=f"Security update applied: {security_metadata.get('update_name', 'Unknown')}",
                            security_metadata=security_metadata
                        )
                        self._send_telemetry(telemetry)
                        
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"❌ Error monitoring build output: {e}")
            
    def _detect_build_phase(self, output_line: str) -> str:
        """Detect current build phase from Packer output."""
        line_lower = output_line.lower()
        
        if "starting vm" in line_lower or "booting from cd-rom" in line_lower:
            return "vm_startup"
        elif "waiting for winrm" in line_lower or "connecting to vm" in line_lower:
            return "connection_setup"
        elif "winrm connected" in line_lower:
            return "provisioning_ready"
        elif "windows update" in line_lower:
            return "windows_updates"
        elif "installing" in line_lower and "turdparty" in line_lower:
            return "turdparty_installation"
        elif "fibratus" in line_lower:
            return "fibratus_setup"
        elif "grpc" in line_lower:
            return "grpc_configuration"
        elif "vagrant" in line_lower and "box" in line_lower:
            return "box_packaging"
        elif "build completed" in line_lower or "build finished" in line_lower:
            return "completion"
        else:
            return "unknown"
            
    def _extract_security_metadata(self, output_line: str) -> Optional[Dict[str, Any]]:
        """Extract security metadata from Windows Update output."""
        if "Found Windows update" in output_line:
            # Parse update information
            # Example: "Found Windows update (2025-06-13; 106731.82 MB): 2025-06 Cumulative Update..."
            try:
                parts = output_line.split("): ")
                if len(parts) >= 2:
                    update_name = parts[1]
                    
                    # Extract date and size
                    info_part = parts[0].split("(")[1] if "(" in parts[0] else ""
                    date_size = info_part.split("; ")
                    
                    metadata = {
                        "update_name": update_name,
                        "update_type": "security" if "Security" in update_name else "feature",
                        "source": "windows_update"
                    }
                    
                    if len(date_size) >= 2:
                        metadata["release_date"] = date_size[0]
                        metadata["size"] = date_size[1]
                        
                    # Classify update type
                    if "Cumulative Update" in update_name:
                        metadata["category"] = "cumulative_update"
                    elif "Security Intelligence" in update_name:
                        metadata["category"] = "security_intelligence"
                    elif ".NET Framework" in update_name:
                        metadata["category"] = "framework_update"
                    else:
                        metadata["category"] = "other"
                        
                    return metadata
                    
            except Exception as e:
                logger.warning(f"⚠️ Failed to parse security metadata: {e}")
                
        return None
        
    async def _handle_build_completion(self, build_id: str):
        """Handle completion of a Packer build."""
        process = self.active_builds.pop(build_id, None)
        if not process:
            return
            
        return_code = process.returncode
        
        # Determine final status
        if return_code == 0:
            status = BuildStatus.COMPLETED
            message = "Packer build completed successfully"
        else:
            status = BuildStatus.FAILED
            message = f"Packer build failed with exit code {return_code}"
            
        # Send completion telemetry
        telemetry = BuildTelemetry(
            build_id=build_id,
            vm_uuid="unknown",  # Would need to track this
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type="build_completed",
            phase="completion",
            status=status,
            message=message
        )
        self._send_telemetry(telemetry)
        
        logger.info(f"✅ Build completed: {build_id} (status: {status.value})")
        
    def _send_telemetry(self, telemetry: BuildTelemetry):
        """Send telemetry to Elasticsearch."""
        try:
            doc = telemetry.to_ecs_format()
            
            # Use date-based index
            date_str = datetime.now().strftime("%Y.%m.%d")
            index_name = f"turdparty-packer-{date_str}"
            
            self.es.index(
                index=index_name,
                body=doc,
                id=f"{telemetry.build_id}-{int(time.time() * 1000)}"
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to send telemetry: {e}")
            
    def clone_box(self, source_box_name: str, target_box_name: str, 
                  modifications: Optional[Dict[str, Any]] = None) -> str:
        """Clone an existing Vagrant box with optional modifications."""
        build_id = f"clone-{uuid.uuid4().hex[:8]}"
        
        config = PackerBuildConfig(
            build_id=build_id,
            build_type=BuildType.CLONE,
            template_name="clone",
            iso_path="",  # Not needed for clones
            output_box_name=target_box_name,
            base_box_id=source_box_name,
            metadata=modifications or {}
        )
        
        return self.queue_build(config)
        
    def update_box(self, box_name: str, update_type: str = "security") -> str:
        """Update an existing box with latest patches."""
        build_id = f"update-{uuid.uuid4().hex[:8]}"
        
        config = PackerBuildConfig(
            build_id=build_id,
            build_type=BuildType.UPDATE,
            template_name="update",
            iso_path="",  # Not needed for updates
            output_box_name=f"{box_name}-updated",
            base_box_id=box_name,
            metadata={"update_type": update_type}
        )
        
        return self.queue_build(config)
        
    def get_build_status(self, build_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a build."""
        if build_id in self.active_builds:
            return {
                "build_id": build_id,
                "status": BuildStatus.BUILDING.value,
                "active": True
            }
        elif build_id in self.build_history:
            return {
                "build_id": build_id,
                "status": self.build_history[build_id].status.value,
                "active": False
            }
        else:
            return None
            
    def list_available_boxes(self) -> List[Dict[str, Any]]:
        """List all available Vagrant boxes."""
        try:
            result = subprocess.run(
                ["vagrant", "box", "list"],
                capture_output=True,
                text=True,
                check=True
            )
            
            boxes = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        boxes.append({
                            "name": parts[0],
                            "provider": parts[1].strip('()'),
                            "version": parts[2] if len(parts) > 2 else "unknown"
                        })
                        
            return boxes
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to list boxes: {e}")
            return []


# Global instance
packer_manager = PackerManager()


async def ensure_box_available(box_name: str, 
                              template_name: str = "windows10",
                              iso_path: str = None) -> str:
    """Ensure a Vagrant box is available, building if necessary."""
    available_boxes = packer_manager.list_available_boxes()
    
    # Check if box already exists
    for box in available_boxes:
        if box["name"] == box_name:
            logger.info(f"✅ Box already available: {box_name}")
            return box_name
            
    # Box doesn't exist, queue build
    logger.info(f"📋 Box not found, queuing build: {box_name}")
    
    if not iso_path:
        # Use default ISO path
        iso_path = "/home/<USER>/dev/10Baht/turdparty-clean/turdparty-collab/isos/Win10_22H2_English_x64v1.iso"
        
    config = PackerBuildConfig(
        build_id=f"auto-{uuid.uuid4().hex[:8]}",
        build_type=BuildType.FRESH,
        template_name=template_name,
        iso_path=iso_path,
        output_box_name=box_name
    )
    
    build_id = packer_manager.queue_build(config)
    
    # Wait for build to complete (with timeout)
    timeout = 7200  # 2 hours
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        status = packer_manager.get_build_status(build_id)
        if status and status["status"] == BuildStatus.COMPLETED.value:
            logger.info(f"✅ Box build completed: {box_name}")
            return box_name
        elif status and status["status"] == BuildStatus.FAILED.value:
            raise RuntimeError(f"Box build failed: {box_name}")
            
        await asyncio.sleep(30)  # Check every 30 seconds
        
    raise TimeoutError(f"Box build timeout: {box_name}")


if __name__ == "__main__":
    # Example usage
    async def main():
        await packer_manager.start_background_worker()
        
        # Queue a build
        config = PackerBuildConfig(
            build_id="test-build-001",
            build_type=BuildType.FRESH,
            template_name="windows10",
            iso_path="/path/to/windows.iso",
            output_box_name="10Baht/windows10-turdparty"
        )
        
        build_id = packer_manager.queue_build(config)
        print(f"Queued build: {build_id}")
        
        # Keep running
        await asyncio.sleep(60)
        await packer_manager.stop_background_worker()
        
    asyncio.run(main())
